<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>NSL Platform - Email Input Function Demo</title>
    <style>
        body {
            font-family: Arial, sans-serif;
            max-width: 800px;
            margin: 0 auto;
            padding: 20px;
            background-color: #f5f5f5;
        }
        .container {
            background: white;
            padding: 30px;
            border-radius: 10px;
            box-shadow: 0 2px 10px rgba(0,0,0,0.1);
        }
        h1 {
            color: #333;
            text-align: center;
            border-bottom: 3px solid #007bff;
            padding-bottom: 10px;
        }
        .form-group {
            margin-bottom: 20px;
        }
        label {
            display: block;
            margin-bottom: 5px;
            font-weight: bold;
            color: #555;
        }
        input[type="text"], input[type="email"], input[type="password"], select {
            width: 100%;
            padding: 10px;
            border: 2px solid #ddd;
            border-radius: 5px;
            font-size: 16px;
            box-sizing: border-box;
        }
        input:focus {
            border-color: #007bff;
            outline: none;
        }
        button {
            background-color: #007bff;
            color: white;
            padding: 12px 30px;
            border: none;
            border-radius: 5px;
            cursor: pointer;
            font-size: 16px;
            margin-right: 10px;
        }
        button:hover {
            background-color: #0056b3;
        }
        .result {
            margin-top: 20px;
            padding: 15px;
            border-radius: 5px;
            white-space: pre-wrap;
            font-family: monospace;
            font-size: 14px;
        }
        .success {
            background-color: #d4edda;
            border: 1px solid #c3e6cb;
            color: #155724;
        }
        .error {
            background-color: #f8d7da;
            border: 1px solid #f5c6cb;
            color: #721c24;
        }
        .tabs {
            display: flex;
            margin-bottom: 20px;
        }
        .tab {
            padding: 10px 20px;
            background-color: #e9ecef;
            border: 1px solid #ddd;
            cursor: pointer;
            border-bottom: none;
        }
        .tab.active {
            background-color: white;
            border-bottom: 1px solid white;
        }
        .tab-content {
            display: none;
            border: 1px solid #ddd;
            padding: 20px;
            border-radius: 0 0 5px 5px;
        }
        .tab-content.active {
            display: block;
        }
    </style>
</head>
<body>
    <div class="container">
        <h1>NSL Platform - Email Input Function Demo</h1>
        
        <div class="tabs">
            <div class="tab active" onclick="showTab('register')">Registration</div>
            <div class="tab" onclick="showTab('login')">Login</div>
            <div class="tab" onclick="showTab('profile')">Profile Update</div>
        </div>
        
        <!-- Registration Tab -->
        <div id="register" class="tab-content active">
            <h3>User Registration Email Input</h3>
            <form id="registerForm">
                <div class="form-group">
                    <label for="reg_email">Email Address:</label>
                    <input type="email" id="reg_email" name="email" placeholder="<EMAIL>" required>
                </div>
                <div class="form-group">
                    <label for="reg_username">Username:</label>
                    <input type="text" id="reg_username" name="username" placeholder="testuser12">
                </div>
                <div class="form-group">
                    <label for="reg_firstname">First Name:</label>
                    <input type="text" id="reg_firstname" name="first_name" placeholder="Durga">
                </div>
                <div class="form-group">
                    <label for="reg_lastname">Last Name:</label>
                    <input type="text" id="reg_lastname" name="last_name" placeholder="Prasad">
                </div>
                <div class="form-group">
                    <label for="reg_tenant">Tenant ID:</label>
                    <input type="text" id="reg_tenant" name="tenant_id" value="t001">
                </div>
                <button type="button" onclick="processEmail('register')">Validate Registration Email</button>
            </form>
        </div>
        
        <!-- Login Tab -->
        <div id="login" class="tab-content">
            <h3>Login Email Input</h3>
            <form id="loginForm">
                <div class="form-group">
                    <label for="login_email">Email Address:</label>
                    <input type="email" id="login_email" name="email" placeholder="<EMAIL>" required>
                </div>
                <div class="form-group">
                    <label for="login_password">Password:</label>
                    <input type="password" id="login_password" name="password" placeholder="secure123">
                </div>
                <button type="button" onclick="processEmail('login')">Validate Login Email</button>
            </form>
        </div>
        
        <!-- Profile Tab -->
        <div id="profile" class="tab-content">
            <h3>Profile Update Email Input</h3>
            <form id="profileForm">
                <div class="form-group">
                    <label for="profile_email">Email Address:</label>
                    <input type="email" id="profile_email" name="email" placeholder="<EMAIL>" required>
                </div>
                <div class="form-group">
                    <label for="profile_userid">User ID:</label>
                    <input type="text" id="profile_userid" name="user_id" placeholder="U14">
                </div>
                <div class="form-group">
                    <label for="profile_firstname">First Name:</label>
                    <input type="text" id="profile_firstname" name="first_name" placeholder="New">
                </div>
                <div class="form-group">
                    <label for="profile_lastname">Last Name:</label>
                    <input type="text" id="profile_lastname" name="last_name" placeholder="User">
                </div>
                <div class="form-group">
                    <label for="profile_tenant">Tenant ID:</label>
                    <input type="text" id="profile_tenant" name="tenant_id" value="t001">
                </div>
                <button type="button" onclick="processEmail('profile')">Validate Profile Email</button>
            </form>
        </div>
        
        <div id="result" class="result" style="display: none;"></div>
    </div>

    <script>
        // Email validation function (JavaScript version)
        function validateEmail(email) {
            const emailPattern = /^[a-zA-Z0-9._%+-]+@[a-zA-Z0-9.-]+\.[a-zA-Z]{2,}$/;
            
            if (!email) {
                return { isValid: false, message: "Email is required" };
            }
            
            if (email.length > 254) {
                return { isValid: false, message: "Email address is too long" };
            }
            
            if (!emailPattern.test(email)) {
                return { isValid: false, message: "Invalid email format" };
            }
            
            if (email.split('@').length !== 2) {
                return { isValid: false, message: "Email must contain exactly one @ symbol" };
            }
            
            const [localPart, domainPart] = email.split('@');
            
            if (localPart.length > 64) {
                return { isValid: false, message: "Local part of email is too long" };
            }
            
            if (domainPart.length > 253) {
                return { isValid: false, message: "Domain part of email is too long" };
            }
            
            return { isValid: true, message: "Valid email" };
        }
        
        // Process email input function
        function processEmailInput(emailData) {
            const email = emailData.email.trim().toLowerCase();
            const validation = validateEmail(email);
            
            if (!validation.isValid) {
                return {
                    status: "error",
                    message: validation.message,
                    data: {},
                    email: "",
                    is_valid: false
                };
            }
            
            const context = emailData.context;
            let result = {
                status: "success",
                email: email,
                is_valid: true,
                context: context
            };
            
            switch (context) {
                case 'register':
                    result.message = "Email validated for registration";
                    result.data = {
                        email: email,
                        username: emailData.username || email.split('@')[0],
                        first_name: emailData.first_name || '',
                        last_name: emailData.last_name || '',
                        tenant_id: emailData.tenant_id || 't001',
                        roles: ['User']
                    };
                    break;
                    
                case 'login':
                    result.message = "Email validated for login";
                    result.data = {
                        username: email,
                        password: emailData.password || '',
                        email: email
                    };
                    break;
                    
                case 'profile':
                    result.message = "Email validated for profile update";
                    result.data = {
                        email: email,
                        user_id: emailData.user_id || '',
                        first_name: emailData.first_name || '',
                        last_name: emailData.last_name || '',
                        tenant_id: emailData.tenant_id || 't001'
                    };
                    break;
                    
                default:
                    result.message = "Email validated successfully";
                    result.data = {
                        email: email,
                        formatted_email: email,
                        domain: email.split('@')[1],
                        local_part: email.split('@')[0]
                    };
            }
            
            return result;
        }
        
        // Tab switching function
        function showTab(tabName) {
            // Hide all tab contents
            const tabContents = document.querySelectorAll('.tab-content');
            tabContents.forEach(content => content.classList.remove('active'));
            
            // Remove active class from all tabs
            const tabs = document.querySelectorAll('.tab');
            tabs.forEach(tab => tab.classList.remove('active'));
            
            // Show selected tab content
            document.getElementById(tabName).classList.add('active');
            
            // Add active class to clicked tab
            event.target.classList.add('active');
            
            // Hide result
            document.getElementById('result').style.display = 'none';
        }
        
        // Process email based on context
        function processEmail(context) {
            const form = document.getElementById(context + 'Form');
            const formData = new FormData(form);
            
            const emailData = {
                context: context
            };
            
            // Convert FormData to object
            for (let [key, value] of formData.entries()) {
                emailData[key] = value;
            }
            
            // Process the email input
            const result = processEmailInput(emailData);
            
            // Display result
            const resultDiv = document.getElementById('result');
            resultDiv.style.display = 'block';
            resultDiv.className = 'result ' + (result.status === 'success' ? 'success' : 'error');
            resultDiv.textContent = JSON.stringify(result, null, 2);
        }
        
        // Auto-fill username from email
        document.getElementById('reg_email').addEventListener('input', function() {
            const email = this.value;
            const username = document.getElementById('reg_username');
            if (email && !username.value) {
                username.value = email.split('@')[0];
            }
        });
    </script>
</body>
</html>
