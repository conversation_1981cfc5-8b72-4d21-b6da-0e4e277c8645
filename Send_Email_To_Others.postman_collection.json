{"info": {"_postman_id": "send-email-others-001", "name": "Send Email to Others - <PERSON><PERSON>", "description": "Send <NAME_EMAIL> to any recipient", "schema": "https://schema.getpostman.com/json/collection/v2.1.0/collection.json"}, "variable": [{"key": "baseurl", "value": "http://localhost:8080", "type": "string"}, {"key": "sender_email", "value": "<EMAIL>", "type": "string"}], "item": [{"name": "Health Check", "request": {"method": "GET", "header": [], "url": {"raw": "{{baseurl}}/health", "host": ["{{baseurl}}"], "path": ["health"]}}, "response": []}, {"name": "Send Custom Email", "request": {"method": "POST", "header": [{"key": "Content-Type", "value": "application/json"}], "body": {"mode": "raw", "raw": "{\n    \"recipient_email\": \"<EMAIL>\",\n    \"recipient_name\": \"Friend Name\",\n    \"subject\": \"Hello from Sivani - NSL Platform\",\n    \"message\": \"Hi! I hope you're doing well. I wanted to share with you that our NSL Platform email system is working perfectly! This email was sent using our new email API.\"\n}"}, "url": {"raw": "{{baseurl}}/send-email", "host": ["{{baseurl}}"], "path": ["send-email"]}}, "response": []}, {"name": "Send Test Email", "request": {"method": "POST", "header": [{"key": "Content-Type", "value": "application/json"}], "body": {"mode": "raw", "raw": "{\n    \"recipient_email\": \"<EMAIL>\",\n    \"template\": \"test\"\n}"}, "url": {"raw": "{{baseurl}}/send-quick", "host": ["{{baseurl}}"], "path": ["send-quick"]}}, "response": []}, {"name": "Send Meeting Invitation", "request": {"method": "POST", "header": [{"key": "Content-Type", "value": "application/json"}], "body": {"mode": "raw", "raw": "{\n    \"recipient_email\": \"<EMAIL>\",\n    \"template\": \"meeting\"\n}"}, "url": {"raw": "{{baseurl}}/send-quick", "host": ["{{baseurl}}"], "path": ["send-quick"]}}, "response": []}, {"name": "Send Project Update", "request": {"method": "POST", "header": [{"key": "Content-Type", "value": "application/json"}], "body": {"mode": "raw", "raw": "{\n    \"recipient_email\": \"<EMAIL>\",\n    \"template\": \"update\"\n}"}, "url": {"raw": "{{baseurl}}/send-quick", "host": ["{{baseurl}}"], "path": ["send-quick"]}}, "response": []}, {"name": "Open Gmail <PERSON>er", "request": {"method": "POST", "header": [{"key": "Content-Type", "value": "application/json"}], "body": {"mode": "raw", "raw": "{}"}, "url": {"raw": "{{baseurl}}/open-gmail", "host": ["{{baseurl}}"], "path": ["open-gmail"]}}, "response": []}]}