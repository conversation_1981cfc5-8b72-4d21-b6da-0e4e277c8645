#!/usr/bin/env python3
"""
Company Email Service System
Professional email service with adapter pattern for enterprise use
"""

import json
import smtplib
import ssl
from email.mime.text import MIMEText
from email.mime.multipart import MIMEMultipart
from email.mime.base import MIMEBase
from email import encoders
import os
import base64
from datetime import datetime
from typing import Dict, Any, List, Optional
import logging

# Configure logging
logging.basicConfig(level=logging.INFO)
logger = logging.getLogger(__name__)

class EmailServiceConfig:
    """Email service configuration"""
    def __init__(self):
        self.smtp_configs = {
            "gmail": {
                "server": "smtp.gmail.com",
                "port": 587,
                "use_tls": True
            },
            "outlook": {
                "server": "smtp-mail.outlook.com", 
                "port": 587,
                "use_tls": True
            },
            "company": {
                "server": "mail.company.com",  # Replace with your company SMTP
                "port": 587,
                "use_tls": True
            }
        }
        
        # Company email settings
        self.company_email = "<EMAIL>"  # Your company email
        self.company_password = "YOUR_APP_PASSWORD_HERE"   # App password
        self.company_name = "NSL Platform"
        self.default_provider = "gmail"

class EmailAdapter:
    """Email adapter for different operations"""
    
    def __init__(self, config: EmailServiceConfig):
        self.config = config
        self.supported_operations = {
            "send_email": self.send_email,
            "send_bulk": self.send_bulk_email,
            "send_template": self.send_template_email,
            "send_notification": self.send_notification,
            "send_welcome": self.send_welcome_email,
            "send_reset": self.send_password_reset
        }
    
    def process_request(self, request_data: Dict[str, Any]) -> Dict[str, Any]:
        """Process email service request"""
        try:
            # Validate request structure
            if not self._validate_request(request_data):
                return self._error_response("Invalid request structure")
            
            adapter_config = request_data.get("adapter_config", {})
            service = adapter_config.get("service")
            operation = adapter_config.get("operation")
            version = adapter_config.get("version", "v1")
            
            # Validate service
            if service != "email_service":
                return self._error_response(f"Unsupported service: {service}")
            
            # Validate operation
            if operation not in self.supported_operations:
                return self._error_response(f"Unsupported operation: {operation}")
            
            # Execute operation
            parameters = request_data.get("parameters", {})
            files = request_data.get("files", {})
            
            logger.info(f"Processing {operation} request")
            result = self.supported_operations[operation](parameters, files)
            
            return {
                "status": "success",
                "service": service,
                "operation": operation,
                "version": version,
                "result": result,
                "timestamp": datetime.now().isoformat()
            }
            
        except Exception as e:
            logger.error(f"Error processing request: {str(e)}")
            return self._error_response(f"Service error: {str(e)}")
    
    def _validate_request(self, request_data: Dict[str, Any]) -> bool:
        """Validate request structure"""
        required_fields = ["adapter_config"]
        for field in required_fields:
            if field not in request_data:
                return False
        
        adapter_config = request_data.get("adapter_config", {})
        required_adapter_fields = ["service", "operation"]
        for field in required_adapter_fields:
            if field not in adapter_config:
                return False
        
        return True
    
    def send_email(self, parameters: Dict[str, Any], files: Dict[str, Any]) -> Dict[str, Any]:
        """Send single email"""
        try:
            # Extract parameters
            to_email = parameters.get("to_email")
            to_name = parameters.get("to_name", "")
            subject = parameters.get("subject", "")
            message = parameters.get("message", "")
            message_type = parameters.get("message_type", "html")
            from_email = parameters.get("from_email", self.config.company_email)
            from_name = parameters.get("from_name", self.config.company_name)
            
            if not to_email:
                return {"success": False, "error": "to_email is required"}
            
            # Send email
            result = self._send_smtp_email(
                from_email=from_email,
                from_name=from_name,
                to_email=to_email,
                to_name=to_name,
                subject=subject,
                message=message,
                message_type=message_type,
                attachments=files
            )
            
            return result
            
        except Exception as e:
            return {"success": False, "error": str(e)}
    
    def send_bulk_email(self, parameters: Dict[str, Any], files: Dict[str, Any]) -> Dict[str, Any]:
        """Send bulk emails"""
        try:
            recipients = parameters.get("recipients", [])
            subject = parameters.get("subject", "")
            message = parameters.get("message", "")
            message_type = parameters.get("message_type", "html")
            
            if not recipients:
                return {"success": False, "error": "recipients list is required"}
            
            results = []
            for recipient in recipients:
                if isinstance(recipient, str):
                    to_email = recipient
                    to_name = ""
                else:
                    to_email = recipient.get("email", "")
                    to_name = recipient.get("name", "")
                
                if to_email:
                    result = self._send_smtp_email(
                        from_email=self.config.company_email,
                        from_name=self.config.company_name,
                        to_email=to_email,
                        to_name=to_name,
                        subject=subject,
                        message=message,
                        message_type=message_type
                    )
                    results.append({
                        "email": to_email,
                        "success": result.get("success", False),
                        "message": result.get("message", "")
                    })
            
            return {
                "success": True,
                "total_sent": len([r for r in results if r["success"]]),
                "total_failed": len([r for r in results if not r["success"]]),
                "results": results
            }
            
        except Exception as e:
            return {"success": False, "error": str(e)}
    
    def send_template_email(self, parameters: Dict[str, Any], files: Dict[str, Any]) -> Dict[str, Any]:
        """Send template-based email"""
        try:
            template_name = parameters.get("template_name", "")
            to_email = parameters.get("to_email", "")
            template_data = parameters.get("template_data", {})
            
            # Get template
            template = self._get_email_template(template_name, template_data)
            if not template:
                return {"success": False, "error": f"Template '{template_name}' not found"}
            
            # Send email with template
            result = self._send_smtp_email(
                from_email=self.config.company_email,
                from_name=self.config.company_name,
                to_email=to_email,
                to_name=template_data.get("user_name", ""),
                subject=template["subject"],
                message=template["message"],
                message_type="html"
            )
            
            return result
            
        except Exception as e:
            return {"success": False, "error": str(e)}
    
    def send_notification(self, parameters: Dict[str, Any], files: Dict[str, Any]) -> Dict[str, Any]:
        """Send notification email"""
        try:
            to_email = parameters.get("to_email", "")
            notification_type = parameters.get("notification_type", "general")
            data = parameters.get("data", {})
            
            # Create notification message
            subject, message = self._create_notification(notification_type, data)
            
            result = self._send_smtp_email(
                from_email=self.config.company_email,
                from_name=f"{self.config.company_name} Notifications",
                to_email=to_email,
                to_name=data.get("user_name", ""),
                subject=subject,
                message=message,
                message_type="html"
            )
            
            return result
            
        except Exception as e:
            return {"success": False, "error": str(e)}
    
    def send_welcome_email(self, parameters: Dict[str, Any], files: Dict[str, Any]) -> Dict[str, Any]:
        """Send welcome email to new users"""
        try:
            to_email = parameters.get("to_email", "")
            user_name = parameters.get("user_name", "")
            user_data = parameters.get("user_data", {})
            
            template_data = {
                "user_name": user_name,
                "company_name": self.config.company_name,
                "login_url": user_data.get("login_url", ""),
                **user_data
            }
            
            return self.send_template_email({
                "template_name": "welcome",
                "to_email": to_email,
                "template_data": template_data
            }, files)
            
        except Exception as e:
            return {"success": False, "error": str(e)}
    
    def send_password_reset(self, parameters: Dict[str, Any], files: Dict[str, Any]) -> Dict[str, Any]:
        """Send password reset email"""
        try:
            to_email = parameters.get("to_email", "")
            reset_token = parameters.get("reset_token", "")
            user_name = parameters.get("user_name", "")
            
            template_data = {
                "user_name": user_name,
                "reset_token": reset_token,
                "reset_url": f"https://your-company.com/reset-password?token={reset_token}",
                "company_name": self.config.company_name
            }
            
            return self.send_template_email({
                "template_name": "password_reset",
                "to_email": to_email,
                "template_data": template_data
            }, files)
            
        except Exception as e:
            return {"success": False, "error": str(e)}
    
    def _send_smtp_email(self, from_email: str, from_name: str, to_email: str, 
                        to_name: str, subject: str, message: str, 
                        message_type: str = "html", attachments: Dict = None) -> Dict[str, Any]:
        """Send email via SMTP"""
        try:
            # Get SMTP configuration
            smtp_config = self.config.smtp_configs[self.config.default_provider]
            
            # Create message
            msg = MIMEMultipart()
            msg['From'] = f"{from_name} <{from_email}>"
            msg['To'] = f"{to_name} <{to_email}>" if to_name else to_email
            msg['Subject'] = subject
            
            # Add body
            if message_type.lower() == 'html':
                msg.attach(MIMEText(message, 'html'))
            else:
                msg.attach(MIMEText(message, 'plain'))
            
            # Add attachments
            if attachments:
                for filename, file_data in attachments.items():
                    self._add_attachment(msg, filename, file_data)
            
            # Send email
            server = smtplib.SMTP(smtp_config['server'], smtp_config['port'])
            if smtp_config['use_tls']:
                server.starttls()
            
            server.login(from_email, self.config.company_password)
            text = msg.as_string()
            server.sendmail(from_email, to_email, text)
            server.quit()
            
            logger.info(f"Email sent successfully to {to_email}")
            
            return {
                "success": True,
                "message": "Email sent successfully",
                "to_email": to_email,
                "subject": subject,
                "timestamp": datetime.now().isoformat()
            }
            
        except Exception as e:
            logger.error(f"Failed to send email to {to_email}: {str(e)}")
            return {
                "success": False,
                "error": str(e),
                "to_email": to_email
            }
    
    def _add_attachment(self, msg: MIMEMultipart, filename: str, file_data: str):
        """Add attachment to email"""
        try:
            # Decode base64 file data
            file_content = base64.b64decode(file_data)
            
            part = MIMEBase('application', 'octet-stream')
            part.set_payload(file_content)
            encoders.encode_base64(part)
            part.add_header(
                'Content-Disposition',
                f'attachment; filename= {filename}'
            )
            msg.attach(part)
            
        except Exception as e:
            logger.error(f"Failed to add attachment {filename}: {str(e)}")
    
    def _get_email_template(self, template_name: str, data: Dict[str, Any]) -> Optional[Dict[str, str]]:
        """Get email template"""
        templates = {
            "welcome": {
                "subject": f"Welcome to {self.config.company_name}!",
                "message": f"""
                <html>
                <body style="font-family: Arial, sans-serif;">
                    <h2>Welcome to {self.config.company_name}, {data.get('user_name', 'User')}!</h2>
                    <p>Thank you for joining us. Your account has been successfully created.</p>
                    <p><strong>Your Details:</strong></p>
                    <ul>
                        <li>Email: {data.get('email', '')}</li>
                        <li>Username: {data.get('username', '')}</li>
                    </ul>
                    <p><a href="{data.get('login_url', '#')}" style="background-color: #007bff; color: white; padding: 10px 20px; text-decoration: none; border-radius: 5px;">Login to Your Account</a></p>
                    <p>Best regards,<br>{self.config.company_name} Team</p>
                </body>
                </html>
                """
            },
            "password_reset": {
                "subject": f"{self.config.company_name} - Password Reset Request",
                "message": f"""
                <html>
                <body style="font-family: Arial, sans-serif;">
                    <h2>Password Reset Request</h2>
                    <p>Hello {data.get('user_name', 'User')},</p>
                    <p>You have requested to reset your password for {self.config.company_name}.</p>
                    <p><a href="{data.get('reset_url', '#')}" style="background-color: #dc3545; color: white; padding: 10px 20px; text-decoration: none; border-radius: 5px;">Reset Password</a></p>
                    <p>If you didn't request this, please ignore this email.</p>
                    <p>This link will expire in 24 hours.</p>
                    <p>Best regards,<br>{self.config.company_name} Team</p>
                </body>
                </html>
                """
            }
        }
        
        return templates.get(template_name)
    
    def _create_notification(self, notification_type: str, data: Dict[str, Any]) -> tuple:
        """Create notification message"""
        notifications = {
            "login": (
                f"{self.config.company_name} - Login Notification",
                f"""
                <html>
                <body style="font-family: Arial, sans-serif;">
                    <h2>Login Notification</h2>
                    <p>Hello {data.get('user_name', 'User')},</p>
                    <p>We detected a login to your account.</p>
                    <p><strong>Details:</strong></p>
                    <ul>
                        <li>Time: {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}</li>
                        <li>IP: {data.get('ip_address', 'Unknown')}</li>
                    </ul>
                    <p>If this wasn't you, please contact support immediately.</p>
                    <p>Best regards,<br>{self.config.company_name} Security Team</p>
                </body>
                </html>
                """
            ),
            "general": (
                f"{self.config.company_name} - Notification",
                f"""
                <html>
                <body style="font-family: Arial, sans-serif;">
                    <h2>Notification</h2>
                    <p>Hello {data.get('user_name', 'User')},</p>
                    <p>{data.get('message', 'You have a new notification.')}</p>
                    <p>Best regards,<br>{self.config.company_name} Team</p>
                </body>
                </html>
                """
            )
        }
        
        return notifications.get(notification_type, notifications["general"])
    
    def _error_response(self, message: str) -> Dict[str, Any]:
        """Create error response"""
        return {
            "status": "error",
            "message": message,
            "timestamp": datetime.now().isoformat()
        }

class CompanyEmailService:
    """Main company email service"""
    
    def __init__(self):
        self.config = EmailServiceConfig()
        self.adapter = EmailAdapter(self.config)
    
    def process_request(self, request_data: Dict[str, Any]) -> Dict[str, Any]:
        """Process email service request"""
        return self.adapter.process_request(request_data)

# Example usage and testing
def test_email_service():
    """Test the email service"""
    service = CompanyEmailService()
    
    # Test send_email operation
    test_request = {
        "adapter_config": {
            "service": "email_service",
            "operation": "send_email",
            "version": "v1"
        },
        "parameters": {
            "to_email": "<EMAIL>",
            "to_name": "Test User",
            "subject": "Test Email from Company Service",
            "message": "<h2>Hello!</h2><p>This is a test email from our company email service.</p>",
            "message_type": "html"
        },
        "files": {}
    }
    
    result = service.process_request(test_request)
    print("Test Result:")
    print(json.dumps(result, indent=2))

if __name__ == "__main__":
    test_email_service()
