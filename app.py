{"info": {"_postman_id": "c0249c73-a330-44eb-b115-481ee3b44afa", "name": "NSL", "schema": "https://schema.getpostman.com/json/collection/v2.1.0/collection.json", "_exporter_id": "29859253", "_collection_link": "https://speeding-satellite-224733.postman.co/workspace/NSL~a82eca32-343f-44d9-b782-a9010ea310de/collection/29859253-c0249c73-a330-44eb-b115-481ee3b44afa?action=share&source=collection_link&creator=29859253"}, "item": [{"name": "<PERSON><PERSON>", "item": [{"name": "GLOBAL OBJECTIVES", "item": [{"name": "global_objectives", "request": {"method": "GET", "header": [{"key": "Authorization", "value": "{{authtoken}}", "type": "text"}], "url": {"raw": "{{baseurl}}api/v2/global_objectives/?tenant_id=t001", "host": ["{{baseurl}}api"], "path": ["v2", "global_objectives", ""], "query": [{"key": "tenant_id", "value": "t001"}]}}, "response": []}, {"name": "global_objectives_basedon_goid", "request": {"method": "GET", "header": [{"key": "Authorization", "value": "{{authtoken}}", "type": "text"}], "url": {"raw": "{{baseurl}}api/v2/global_objectives/GO1?tenant_id=t001", "host": ["{{baseurl}}api"], "path": ["v2", "global_objectives", "GO1"], "query": [{"key": "tenant_id", "value": "t001"}]}}, "response": []}, {"name": "books", "request": {"method": "GET", "header": [{"key": "Authorization", "value": "{{authtoken}}", "type": "text"}], "url": {"raw": "{{baseurl}}api/v2/global_objectives/books/all?tenant_id=t001", "host": ["{{baseurl}}api"], "path": ["v2", "global_objectives", "books", "all"], "query": [{"key": "tenant_id", "value": "t001"}]}}, "response": []}, {"name": "chapters", "request": {"method": "GET", "header": [{"key": "Authorization", "value": "{{authtoken}}", "type": "text"}], "url": {"raw": "{{baseurl}}api/v2/global_objectives/books/b001/chapters?tenant_id=t001", "host": ["{{baseurl}}api"], "path": ["v2", "global_objectives", "books", "b001", "chapters"], "query": [{"key": "tenant_id", "value": "t001"}]}}, "response": []}, {"name": "objectives", "request": {"method": "GET", "header": [{"key": "Authorization", "value": "{{authtoken}}", "type": "text"}], "url": {"raw": "{{baseurl}}api/v2/global_objectives/books/b001/objectives?tenant_id=t001", "host": ["{{baseurl}}api"], "path": ["v2", "global_objectives", "books", "b001", "objectives"], "query": [{"key": "tenant_id", "value": "t001"}]}}, "response": []}, {"name": "objectivesbychapter", "request": {"method": "GET", "header": [{"key": "Authorization", "value": "{{authtoken}}", "type": "text"}], "url": {"raw": "{{baseurl}}api/v2/global_objectives/chapters/c001/objectives?tenant_id=t001", "host": ["{{baseurl}}api"], "path": ["v2", "global_objectives", "chapters", "c001", "objectives"], "query": [{"key": "tenant_id", "value": "t001"}]}}, "response": []}]}, {"name": "Workflow", "item": [{"name": "workflow_instances", "request": {"method": "POST", "header": [{"key": "Authorization", "value": "{{authtoken}}", "type": "text"}], "body": {"mode": "raw", "raw": "{\"user_id\": \"U29\"}", "options": {"raw": {"language": "json"}}}, "url": {"raw": "{{baseurl}}api/v2/workflow_instances/GO1/start?tenant_id=t001", "host": ["{{baseurl}}api"], "path": ["v2", "workflow_instances", "GO1", "start"], "query": [{"key": "tenant_id", "value": "t001"}]}}, "response": []}, {"name": "workflow_instances Get", "protocolProfileBehavior": {"disableBodyPruning": true}, "request": {"method": "GET", "header": [{"key": "Authorization", "value": "{{authtoken}}", "type": "text"}], "body": {"mode": "raw", "raw": "", "options": {"raw": {"language": "json"}}}, "url": {"raw": "{{baseurl}}api/v2/workflow_instances/?tenant_id=t001", "host": ["{{baseurl}}api"], "path": ["v2", "workflow_instances", ""], "query": [{"key": "tenant_id", "value": "t001"}]}}, "response": []}, {"name": "workflow_instancesCreate", "request": {"method": "POST", "header": [{"key": "Authorization", "value": "{{authtoken}}", "type": "text"}], "body": {"mode": "raw", "raw": "{\r\n  \"go_id\": \"GO1\",\r\n  \"tenant_id\": \"t001\", \r\n  \"user_id\": \"U29\",\r\n  \"test_mode\": false\r\n}", "options": {"raw": {"language": "json"}}}, "url": {"raw": "{{baseurl}}api/v2/workflow_instances/?tenant_id=t001", "host": ["{{baseurl}}api"], "path": ["v2", "workflow_instances", ""], "query": [{"key": "tenant_id", "value": "t001"}]}}, "response": []}]}, {"name": "Local Objective", "item": [{"name": "local_objectives", "request": {"method": "GET", "header": [{"key": "Authorization", "value": "{{authtoken}}", "type": "text"}], "url": {"raw": "{{baseurl}}api/v2/local_objectives/instances/d621b1ff-360b-435e-b7cc-e8639329201c/inputs?tenant_id=t001", "host": ["{{baseurl}}api"], "path": ["v2", "local_objectives", "instances", "d621b1ff-360b-435e-b7cc-e8639329201c", "inputs"], "query": [{"key": "tenant_id", "value": "t001"}]}}, "response": []}, {"name": "dependent-options", "request": {"method": "GET", "header": [{"key": "Authorization", "value": "{{authtoken}}", "type": "text"}], "url": {"raw": "{{baseurl}}api/v2/local_objectives/instances/d621b1ff-360b-435e-b7cc-e8639329201c/inputs/GO1.LO1.IP1.IT8/dependent-options?parent_value=sick&tenant_id=t001", "host": ["{{baseurl}}api"], "path": ["v2", "local_objectives", "instances", "d621b1ff-360b-435e-b7cc-e8639329201c", "inputs", "GO1.LO1.IP1.IT8", "dependent-options"], "query": [{"key": "parent_value", "value": "sick"}, {"key": "tenant_id", "value": "t001"}]}}, "response": []}]}, {"name": "MannualCreation", "item": [{"name": "roles", "request": {"method": "POST", "header": [], "body": {"mode": "formdata", "formdata": [{"key": "text", "value": "Role Employee inherits [None]:\n* Core Responsibilities:\n  - Create LeaveApplications\n  - Read LeaveApplications\n  - Update own Employee information\n  - Participate in workflow go001 as Originator\n* Reports to: Manager\n* Organizational Level: Individual\n* Department: Sales\n* Team: Lassi Sales\n* Key Performance Indicators:\n  - Leave Utilization: Measures the percentage of allocated leave days used by the employee\n    - Formula: Employee.usedLeaveDays / Employee.allocatedLeaveDays * 100\n    - Target: 80-100%\n    - Measurement Frequency: Quarterly\n  - Task Completion Rate: Measures the percentage of assigned tasks completed on time\n    - Formula: COUNT(Task WHERE Task.status = 'completed' AND Task.completionDate <= Task.dueDate) / COUNT(Task) * 100\n    - Target: 95%\n    - Measurement Frequency: Monthly\n* Decision Authority:\n  - Personal leave requests: up to allocated days\n  - Personal task prioritization: within assigned workload\n\nRole Manager inherits Employee:\n* Core Responsibilities:\n  - Create and manage PerformanceReviews for team members\n  - Create and manage TeamBudget\n  - Read and analyze TeamMetrics\n  - Process owner for workflow go001 and originator for go002\n* Reports to: Director\n* Organizational Level: Management\n* Department: Sales\n* Team: Lassi Sales\n* Key Performance Indicators:\n  - Team Performance: Measures the average performance rating of team members\n    - Formula: AVG(PerformanceReview.rating) GROUP BY Employee.team\n    - Target: 4.0/5.0\n    - Measurement Frequency: Quarterly\n  - Budget Adherence: Measures how well the team stays within budget\n    - Formula: TeamBudget.actualSpend / TeamBudget.allocatedAmount * 100\n    - Target: ≤ 100%\n    - Measurement Frequency: Monthly\n  - Team Retention: Measures the percentage of team members retained over time\n    - Formula: COUNT(Employee WHERE Employee.status = 'active' AND Employee.tenureMonths > 12) / COUNT(Employee) * 100\n    - Target: 85%\n    - Measurement Frequency: Quarterly\n* Decision Authority:\n  - Budget approval: up to $10K\n  - Team member leave approval: all allocated days\n  - Performance review finalization: for all direct reports\n\nRole HRManager inherits Manager:\n* Core Responsibilities:\n  - Create and manage EmployeeRecords\n  - Develop and update CompensationPlans\n  - Review Department metrics\n  - Business sponsor for workflow go001 and process owner for go003\n* Reports to: CHRO\n* Organizational Level: Management\n* Department: Human Resources\n* Team: HR Management\n* Key Performance Indicators:\n  - Compensation Equity: Measures the variance in compensation across similar roles\n    - Formula: STDEV(CompensationPlan.salary) / AVG(CompensationPlan.salary) GROUP BY Employee.jobTitle\n    - Target: < 15%\n    - Measurement Frequency: Quarterly\n  - Employee Satisfaction: Measures overall employee satisfaction based on surveys\n    - Formula: AVG(EmployeeSurvey.satisfactionScore)\n    - Target: > 4.0/5.0\n    - Measurement Frequency: Quarterly\n  - Time-to-Hire: Measures the average time to fill open positions\n    - Formula: AVG(Recruitment.hireDate - Recruitment.openDate)\n    - Target: < 45 days\n    - Measurement Frequency: Monthly\n* Decision Authority:\n  - Salary approval: all levels\n  - Termination processing: organization-wide\n  - Compensation plan adjustments: up to department level\n\nRole FinanceManager inherits [None]:\n* Core Responsibilities:\n  - Create and manage Budget, Expense, and Revenue records\n  - Review Department and TeamBudget information\n  - Review PerformanceReviews for financial planning\n  - Process owner for workflow go002 and business sponsor for go004\n* Reports to: CFO\n* Organizational Level: Management\n* Department: Finance\n* Team: Financial Management\n* Key Performance Indicators:\n  - Budget Accuracy: Measures the accuracy of budget forecasts\n    - Formula: ABS(Budget.actual - Budget.forecast) / Budget.forecast * 100\n    - Target: < 10%\n    - Measurement Frequency: Quarterly\n  - Expense Reduction: Measures the reduction in operational expenses\n    - Formula: (Expense.previousPeriod - Expense.currentPeriod) / Expense.previousPeriod * 100\n    - Target: > 5%\n    - Measurement Frequency: Quarterly\n  - Revenue Growth: Measures the growth in revenue over time\n    - Formula: (Revenue.currentPeriod - Revenue.previousPeriod) / Revenue.previousPeriod * 100\n    - Target: > 8% annually\n    - Measurement Frequency: Monthly\n* Decision Authority:\n  - Budget approval: up to $100K\n  - Expense authorization: organization-wide\n  - Financial reporting adjustments: all levels\n\nRole SystemAdmin inherits [None]:\n* Core Responsibilities:\n  - Create and manage User, Role, and Permission records\n  - Monitor and analyze SystemLogs\n  - Configure and update SystemConfiguration\n  - Process owner for workflow go005\n* Reports to: CTO\n* Organizational Level: Team\n* Department: IT\n* Team: System Administration\n* Key Performance Indicators:\n  - System Uptime: Measures the percentage of time systems are operational\n    - Formula: SystemLog.uptimeMinutes / (SystemLog.totalMinutes) * 100\n    - Target: 99.9%\n    - Measurement Frequency: Daily\n  - Security Incident Resolution: Measures the average time to resolve security incidents\n    - Formula: AVG(SecurityIncident.resolutionTime)\n    - Target: < 4 hours\n    - Measurement Frequency: Weekly\n  - User Satisfaction: Measures user satisfaction with system performance\n    - Formula: AVG(UserSurvey.satisfactionScore)\n    - Target: > 4.2/5.0\n    - Measurement Frequency: Monthly\n* Decision Authority:\n  - System configuration: all systems\n  - User management: creation, modification, and deletion\n  - Permission assignment: all roles and permissions\n\nUser JohnDoe:\n* Personal Information:\n  - Full Name: John Doe\n  - Email: <EMAIL>\n  - Phone: ******-123-4567\n  - Employee ID: EMP001\n* Authentication:\n  - Username: jdoe\n  - Password Policy: Strong\n  - Multi-Factor Authentication: Enabled\n  - Last Password Change: 2025-04-15\n* Role Assignments:\n  - Primary Role: Manager\n  - Secondary Roles: Employee\n* Department: Sales\n* Team: Lassi Sales\n* Reporting Structure:\n  - Reports to: SamJohnson\n  - Direct Reports: AliceGarcia\n* Access Control:\n  - Account Status: Active\n  - Access Level: Standard\n  - IP Restrictions: Any\n  - Time Restrictions: Business Hours Only\n* System Permissions:\n  - Data Access Scope: Team\n  - Special Permissions: Budget Approval, Performance Review\n* Activity Tracking:\n  - Account Created: 2023-01-10\n  - Last Login: 2025-05-15\n  - Last Activity: 2025-05-16\n  - Session Timeout: 30\n* Compliance:\n  - Training Status: Complete\n  - Agreement Acceptance: Accepted\n  - Certification Status: Valid\n\nUser AliceGarcia:\n* Personal Information:\n  - Full Name: Alice Garcia\n  - Email: <EMAIL>\n  - Phone: ******-987-6543\n  - Employee ID: EMP002\n* Authentication:\n  - Username: agarcia\n  - Password Policy: Strong\n  - Multi-Factor Authentication: Enabled\n  - Last Password Change: 2025-04-20\n* Role Assignments:\n  - Primary Role: Employee\n  - Secondary Roles: [None]\n* Department: Sales\n* Team: Lassi Sales\n* Reporting Structure:\n  - Reports to: JohnDoe\n  - Direct Reports: [None]\n* Access Control:\n  - Account Status: Active\n  - Access Level: Standard\n  - IP Restrictions: Any\n  - Time Restrictions: Any\n* System Permissions:\n  - Data Access Scope: Own\n  - Special Permissions: [None]\n* Activity Tracking:\n  - Account Created: 2024-06-15\n  - Last Login: 2025-05-14\n  - Last Activity: 2025-05-14\n  - Session Timeout: 30\n* Compliance:\n  - Training Status: Complete\n  - Agreement Acceptance: Accepted\n  - Certification Status: Valid\n\nUser SamJohnson:\n* Personal Information:\n  - Full Name: Sam Johnson\n  - Email: <EMAIL>\n  - Phone: ******-456-7890\n  - Employee ID: EMP003\n* Authentication:\n  - Username: sjohnson\n  - Password Policy: Strong\n  - Multi-Factor Authentication: Enabled\n  - Last Password Change: 2025-03-10\n* Role Assignments:\n  - Primary Role: HRManager\n  - Secondary Roles: Manager\n* Department: Human Resources\n* Team: HR Management\n* Reporting Structure:\n  - Reports to: MariaTech\n  - Direct Reports: JohnDoe\n* Access Control:\n  - Account Status: Active\n  - Access Level: Administrative\n  - IP Restrictions: Any\n  - Time Restrictions: Any\n* System Permissions:\n  - Data Access Scope: Organization\n  - Special Permissions: Salary Approval, Termination Processing\n* Activity Tracking:\n  - Account Created: 2022-11-05\n  - Last Login: 2025-05-16\n  - Last Activity: 2025-05-16\n  - Session Timeout: 45\n* Compliance:\n  - Training Status: Complete\n  - Agreement Acceptance: Accepted\n  - Certification Status: Valid\n\nUser MariaTech:\n* Personal Information:\n  - Full Name: Maria Tech\n  - Email: <EMAIL>\n  - Phone: ******-789-0123\n  - Employee ID: EMP004\n* Authentication:\n  - Username: mtech\n  - Password Policy: Strong\n  - Multi-Factor Authentication: Enabled\n  - Last Password Change: 2025-05-01\n* Role Assignments:\n  - Primary Role: SystemAdmin\n  - Secondary Roles: [None]\n* Department: IT\n* Team: System Administration\n* Reporting Structure:\n  - Direct Reports: SamJohnson\n* Access Control:\n  - Account Status: Active\n  - Access Level: Administrative\n  - IP Restrictions: Specific IPs\n  - Time Restrictions: Any\n* System Permissions:\n  - Data Access Scope: System\n  - Special Permissions: System Configuration, User Management\n* Activity Tracking:\n  - Account Created: 2023-08-20\n  - Last Login: 2025-05-16\n  - Last Activity: 2025-05-16\n  - Session Timeout: 15\n* Compliance:\n  - Training Status: Complete\n  - Agreement Acceptance: Accepted\n  - Certification Status: Valid\n", "type": "text"}, {"key": "test", "value": "", "type": "text", "disabled": true}]}, "url": {"raw": "{{baseurlCreationManual}}api/roles/validate", "host": ["{{baseurlCreationManual}}api"], "path": ["roles", "validate"]}}, "response": []}, {"name": "Entitites", "request": {"method": "POST", "header": [], "body": {"mode": "formdata", "formdata": [{"key": "text", "value": "Loan has loanId^PK, customerId^FK, loanAmount, interestRate, term, startDate, endDate, status (Active, Closed, Default, Restructured), paymentFrequency (Monthly, Biweekly, Weekly, Quarterly, Annually), totalPaymentsMade, remainingBalance[derived], loanType (Personal, Mortgage, Auto, Education, Business), collateralId^FK, originationFee, lateFeePercentage, earlyPaymentPenalty.\n\nRelationships for Loan:\n-<PERSON><PERSON> has many-to-one relationship with Customer using Loan.customerId to Customer.customerId^PK\n-<PERSON><PERSON> has many-to-one relationship with Collateral using Loan.collateralId to Collateral.collateralId^PK\n\nValidations for Loan:\n-Loan.loanId must be unique\n-Loan.loanAmount must be greater than 0\n-Loan.interestRate must be greater than or equal to 1.0\n-Loan.term must be greater than or equal to 3\n-Loan.startDate must not be in the future\n-Loan.endDate must be after startDate\n\nEntity Additional Properties:\n-Display Name: Loan Agreement\n-Type: Core Entity\n-Description: Represents a loan agreement between the financial institution and a customer\n\nAttribute Additional Properties:\n-Attribute name: loanId\n-Key: Primary\n-Display Name: Loan ID\n-DataType: Integer\n-Required: Mandatory\n-Format: \"LN-######\"\n-Values: N/A\n-Default: N/A\n-Validation: Auto-increment\n-Error Message: \"Invalid Loan ID format\"\n-Description: Unique identifier for the loan\n\nAttribute Additional Properties:\n-Attribute name: customerId\n-Key: Foreign\n-Display Name: Customer ID\n-DataType: Integer\n-Required: Mandatory\n-Format: \"CUS-######\"\n-Values: N/A\n-Default: N/A\n-Validation: Foreign Key Check\n-Error Message: \"Customer ID must reference an existing customer\"\n-Description: References the customer who took the loan\n\nAttribute Additional Properties:\n-Attribute name: loanAmount\n-Key: Non-unique\n-Display Name: Principal Loan Amount\n-DataType: Decimal\n-Required: Mandatory\n-Format: \"$#,###.00\"\n-Values: 1000.00-10000000.00\n-Default: N/A\n-Validation: Range Check\n-Error Message: \"Loan amount must be between $1,000 and $10,000,000\"\n-Description: The principal amount borrowed by the customer\n\nAttribute Additional Properties:\n-Attribute name: interestRate\n-Key: Non-unique\n-Display Name: Annual Interest Rate (%)\n-DataType: Decimal\n-Required: Mandatory\n-Format: \"0.00%\"\n-Values: 1.00-30.00\n-Default: \"5.99\"\n-Validation: Range Check\n-Error Message: \"Interest rate must be between 1.00% and 30.00%\"\n-Description: Annual interest rate applied to the loan\n\nAttribute Additional Properties:\n-Attribute name: term\n-Key: Non-unique\n-Display Name: Loan Term (Months)\n-DataType: Integer\n-Required: Mandatory\n-Format: \"### months\"\n-Values: 3-480\n-Default: \"60\"\n-Validation: Range Check\n-Error Message: \"Term must be between 3 and 480 months\"\n-Description: Duration of the loan in months\n\nAttribute Additional Properties:\n-Attribute name: startDate\n-Key: Non-unique\n-Display Name: Loan Start Date\n-DataType: Date\n-Required: Mandatory\n-Format: \"YYYY-MM-DD\"\n-Values: N/A\n-Default: \"CURRENT_DATE\"\n-Validation: Date Check\n-Error Message: \"Start date cannot be in the future\"\n-Description: Date when the loan becomes active\n\nAttribute Additional Properties:\n-Attribute name: endDate\n-Key: Non-unique\n-Display Name: Loan End Date\n-DataType: Date\n-Required: Mandatory\n-Format: \"YYYY-MM-DD\"\n-Values: N/A\n-Default: N/A\n-Validation: Date Check\n-Error Message: \"End date must be after start date\"\n-Description: Scheduled date for loan completion\n\nAttribute Additional Properties:\n-Attribute name: status\n-Key: Non-unique\n-Display Name: Loan Status\n-DataType: Enum\n-Required: Mandatory\n-Format: N/A\n-Values: \"Active\", \"Closed\", \"Default\", \"Restructured\"\n-Default: \"Active\"\n-Validation: List Check\n-Error Message: \"Invalid loan status\"\n-Description: Current status of the loan\n\nAttribute Additional Properties:\n-Attribute name: paymentFrequency\n-Key: Non-unique\n-Display Name: Payment Frequency\n-DataType: Enum\n-Required: Mandatory\n-Format: N/A\n-Values: \"Monthly\", \"Biweekly\", \"Weekly\", \"Quarterly\", \"Annually\"\n-Default: \"Monthly\"\n-Validation: List Check\n-Error Message: \"Invalid payment frequency\"\n-Description: How often payments are made on the loan\n\nAttribute Additional Properties:\n-Attribute name: totalPaymentsMade\n-Key: Non-unique\n-Display Name: Total Payments Made\n-DataType: Integer\n-Required: Mandatory\n-Format: \"###\"\n-Values: 0-N\n-Default: \"0\"\n-Validation: Range Check\n-Error Message: \"Total payments cannot be negative\"\n-Description: Number of payments completed for this loan\n\nAttribute Additional Properties:\n-Attribute name: remainingBalance\n-Key: Non-unique\n-Display Name: Remaining Balance\n-DataType: Decimal\n-Required: Calculated\n-Format: \"$#,###.00\"\n-Values: 0.00-N\n-Default: N/A\n-Validation: Calculation\n-Error Message: N/A\n-Description: Current outstanding balance on the loan\n\nAttribute Additional Properties:\n-Attribute name: loanType\n-Key: Non-unique\n-Display Name: Loan Type\n-DataType: Enum\n-Required: Mandatory\n-Format: N/A\n-Values: \"Personal\", \"Mortgage\", \"Auto\", \"Education\", \"Business\"\n-Default: \"Personal\"\n-Validation: List Check\n-Error Message: \"Invalid loan type\"\n-Description: Category of loan based on purpose\n\nAttribute Additional Properties:\n-Attribute name: collateralId\n-Key: Foreign\n-Display Name: Collateral ID\n-DataType: Integer\n-Required: Optional\n-Format: \"COL-######\"\n-Values: N/A\n-Default: N/A\n-Validation: Foreign Key Check\n-Error Message: \"Collateral ID must reference existing collateral\"\n-Description: References the asset used to secure the loan\n\nAttribute Additional Properties:\n-Attribute name: originationFee\n-Key: Non-unique\n-Display Name: Origination Fee\n-DataType: Decimal\n-Required: Optional\n-Format: \"$#,###.00\"\n-Values: 0.00-N\n-Default: \"0.00\"\n-Validation: Range Check\n-Error Message: \"Origination fee cannot be negative\"\n-Description: Fee charged for processing a new loan application\n\nAttribute Additional Properties:\n-Attribute name: lateFeePercentage\n-Key: Non-unique\n-Display Name: Late Fee Percentage\n-DataType: Decimal\n-Required: Optional\n-Format: \"0.00%\"\n-Values: 0.00-20.00\n-Default: \"5.00\"\n-Validation: Range Check\n-Error Message: \"Late fee percentage must be between 0% and 20%\"\n-Description: Percentage charged on late payments\n\nAttribute Additional Properties:\n-Attribute name: earlyPaymentPenalty\n-Key: Non-unique\n-Display Name: Early Payment Penalty\n-DataType: Decimal\n-Required: Optional\n-Format: \"0.00%\"\n-Values: 0.00-10.00\n-Default: \"1.00\"\n-Validation: Range Check\n-Error Message: \"Early payment penalty must be between 0% and 10%\"\n-Description: Penalty charged for early loan payoff\n\nRelationship: Loan to Customer\nRelationship Properties:\n-On Delete: Restrict (Prevent deletion of customer with active loans)\n-On Update: Cascade (Update loan records when customer details change)\n-Foreign Key Type: Non-Nullable\n\nRelationship: Loan to Collateral\nRelationship Properties:\n-On Delete: Set Null (Remove loan reference when collateral is deleted)\n-On Update: Cascade (Update loan records when collateral details change)\n-Foreign Key Type: Nullable\n\nCustomer has customerId^PK, firstName, lastName, email, phone, address, city, state, zipCode, country, dateOfBirth, ssn, creditScore, annualIncome, employmentStatus (Employed, Self-Employed, Unemployed, Retired), employerName, employmentLength, customerType (Individual, Business), status (Active, Inactive, Suspended).\nRelationships for Customer:\n-Customer has one-to-many relationship with Loan using Customer.customerId to Loan.customerId^FK\n-Customer has one-to-many relationship with Collateral using Customer.customerId to Collateral.customerId^FK\n\nValidations for Customer:\n-Customer.customerId must be unique\n-Customer.email must be a valid email format\n-Customer.ssn must be a valid format and unique\n-Customer.creditScore must be between 300 and 850\n-Customer.annualIncome must be greater than 0\n-Customer.dateOfBirth must indicate customer is at least 18 years old\n\nEntity Additional Properties:\n-Display Name: Customer\n-Type: Core Entity\n-Description: Represents a customer of the financial institution\n\nAttribute Additional Properties:\n-Attribute name: customerId\n-Key: Primary\n-Display Name: Customer ID\n-DataType: Integer\n-Required: Mandatory\n-Format: \"CUS-######\"\n-Values: N/A\n-Default: N/A\n-Validation: Auto-increment\n-Error Message: \"Invalid Customer ID format\"\n-Description: Unique identifier for the customer\n\nAttribute Additional Properties:\n-Attribute name: firstName\n-Key: Non-unique\n-Display Name: First Name\n-DataType: String\n-Required: Mandatory\n-Format: N/A\n-Values: N/A\n-Default: N/A\n-Validation: Length Check\n-Error Message: \"First name is required\"\n-Description: Customer's first name\n\nAttribute Additional Properties:\n-Attribute name: lastName\n-Key: Non-unique\n-Display Name: Last Name\n-DataType: String\n-Required: Mandatory\n-Format: N/A\n-Values: N/A\n-Default: N/A\n-Validation: Length Check\n-Error Message: \"Last name is required\"\n-Description: Customer's last name\n\nAttribute Additional Properties:\n-Attribute name: email\n-Key: Non-unique\n-Display Name: Email Address\n-DataType: String\n-Required: Mandatory\n-Format: \"<EMAIL>\"\n-Values: N/A\n-Default: N/A\n-Validation: Format Check\n-Error Message: \"Invalid email format\"\n-Description: Customer's email address\n\nAttribute Additional Properties:\n-Attribute name: phone\n-Key: Non-unique\n-Display Name: Phone Number\n-DataType: String\n-Required: Mandatory\n-Format: \"+#-###-###-####\"\n-Values: N/A\n-Default: N/A\n-Validation: Format Check\n-Error Message: \"Invalid phone number format\"\n-Description: Customer's contact phone number\n\nAttribute Additional Properties:\n-Attribute name: address\n-Key: Non-unique\n-Display Name: Street Address\n-DataType: String\n-Required: Mandatory\n-Format: N/A\n-Values: N/A\n-Default: N/A\n-Validation: Length Check\n-Error Message: \"Address is required\"\n-Description: Customer's street address\n\nAttribute Additional Properties:\n-Attribute name: city\n-Key: Non-unique\n-Display Name: City\n-DataType: String\n-Required: Mandatory\n-Format: N/A\n-Values: N/A\n-Default: N/A\n-Validation: Length Check\n-Error Message: \"City is required\"\n-Description: Customer's city of residence\n\nAttribute Additional Properties:\n-Attribute name: state\n-Key: Non-unique\n-Display Name: State/Province\n-DataType: String\n-Required: Mandatory\n-Format: N/A\n-Values: N/A\n-Default: N/A\n-Validation: Length Check\n-Error Message: \"State is required\"\n-Description: Customer's state of residence\n\nAttribute Additional Properties:\n-Attribute name: zipCode\n-Key: Non-unique\n-Display Name: ZIP/Postal Code\n-DataType: String\n-Required: Mandatory\n-Format: \"#####\" or \"#####-####\"\n-Values: N/A\n-Default: N/A\n-Validation: Format Check\n-Error Message: \"Invalid zip code format\"\n-Description: Customer's zip code\n\nAttribute Additional Properties:\n-Attribute name: country\n-Key: Non-unique\n-Display Name: Country\n-DataType: String\n-Required: Mandatory\n-Format: N/A\n-Values: N/A\n-Default: \"USA\"\n-Validation: Length Check\n-Error Message: \"Country is required\"\n-Description: Customer's country of residence\n\nAttribute Additional Properties:\n-Attribute name: dateOfBirth\n-Key: Non-unique\n-Display Name: Date of Birth\n-DataType: Date\n-Required: Mandatory\n-Format: \"YYYY-MM-DD\"\n-Values: N/A\n-Default: N/A\n-Validation: Age Check\n-Error Message: \"Customer must be at least 18 years old\"\n-Description: Customer's date of birth\n\nAttribute Additional Properties:\n-Attribute name: ssn\n-Key: Non-unique\n-Display Name: Social Security Number\n-DataType: String\n-Required: Mandatory\n-Format: \"###-##-####\"\n-Values: N/A\n-Default: N/A\n-Validation: Format Check\n-Error Message: \"Invalid SSN format\"\n-Description: Customer's Social Security Number\n\nAttribute Additional Properties:\n-Attribute name: creditScore\n-Key: Non-unique\n-Display Name: Credit Score\n-DataType: Integer\n-Required: Mandatory\n-Format: \"###\"\n-Values: 300-850\n-Default: N/A\n-Validation: Range Check\n-Error Message: \"Credit score must be between 300 and 850\"\n-Description: Customer's credit score\n\nAttribute Additional Properties:\n-Attribute name: annualIncome\n-Key: Non-unique\n-Display Name: Annual Income\n-DataType: Decimal\n-Required: Mandatory\n-Format: \"$#,###.00\"\n-Values: 0.00-N\n-Default: N/A\n-Validation: Range Check\n-Error Message: \"Annual income must be greater than 0\"\n-Description: Customer's annual income\n\nAttribute Additional Properties:\n-Attribute name: employmentStatus\n-Key: Non-unique\n-Display Name: Employment Status\n-DataType: Enum\n-Required: Mandatory\n-Format: N/A\n-Values: \"Employed\", \"Self-Employed\", \"Unemployed\", \"Retired\"\n-Default: \"Employed\"\n-Validation: List Check\n-Error Message: \"Invalid employment status\"\n-Description: Customer's current employment status\n\nAttribute Additional Properties:\n-Attribute name: employerName\n-Key: Non-unique\n-Display Name: Employer Name\n-DataType: String\n-Required: Optional\n-Format: N/A\n-Values: N/A\n-Default: N/A\n-Validation: Length Check\n-Error Message: N/A\n-Description: Name of customer's employer\n\nAttribute Additional Properties:\n-Attribute name: employmentLength\n-Key: Non-unique\n-Display Name: Employment Length\n-DataType: Integer\n-Required: Optional\n-Format: \"## years\"\n-Values: 0-N\n-Default: \"0\"\n-Validation: Range Check\n-Error Message: \"Employment length cannot be negative\"\n-Description: Length of employment in years\n\nAttribute Additional Properties:\n-Attribute name: customerType\n-Key: Non-unique\n-Display Name: Customer Type\n-DataType: Enum\n-Required: Mandatory\n-Format: N/A\n-Values: \"Individual\", \"Business\"\n-Default: \"Individual\"\n-Validation: List Check\n-Error Message: \"Invalid customer type\"\n-Description: Type of customer\n\nAttribute Additional Properties:\n-Attribute name: status\n-Key: Non-unique\n-Display Name: Account Status\n-DataType: Enum\n-Required: Mandatory\n-Format: N/A\n-Values: \"Active\", \"Inactive\", \"Suspended\"\n-Default: \"Active\"\n-Validation: List Check\n-Error Message: \"Invalid status\"\n-Description: Current status of the customer account\n\nRelationship: Customer to Loan\nRelationship Properties:\n-On Delete: Restrict (Prevent deletion of customer with active loans)\n-On Update: Cascade (Update loan records when customer details change)\n-Foreign Key Type: Non-Nullable\n\nCollateral has collateralId^PK, customerId^FK, loanId^FK, collateralType (RealEstate, Vehicle, Investment, Cash, Other), description, value, appraisalDate, appraisalValue, lienStatus (FirstLien, SecondLien, NoLien), condition (Excellent, Good, Fair, Poor), assetAddress, assetCity, assetState, assetZipCode, insuranceProvider, insurancePolicyNumber, insuranceExpirationDate, documentationComplete (true, false).\n\nRelationships for Collateral:\n-Collateral has many-to-one relationship with Customer using Collateral.customerId to Customer.customerId^PK\n-Collateral has many-to-one relationship with Loan using Collateral.loanId to Loan.loanId^PK\n\nValidations for Collateral:\n-Collateral.collateralId must be unique\n-Collateral.customerId must exist in Customer table\n-Collateral.loanId must exist in Loan table\n-Collateral.value must be greater than 0\n-Collateral.appraisalValue must be greater than 0\n-Collateral.customerId must match the Loan.customerId of the associated loan\n\nEntity Additional Properties:\n-Display Name: Collateral Asset\n-Type: Core Entity\n-Description: Represents an asset used to secure a loan\n\nAttribute Additional Properties:\n-Attribute name: collateralId\n-Key: Primary\n-Display Name: Collateral ID\n-DataType: Integer\n-Required: Mandatory\n-Format: \"COL-######\"\n-Values: N/A\n-Default: N/A\n-Validation: Auto-increment\n-Error Message: \"Invalid Collateral ID format\"\n-Description: Unique identifier for the collateral\n\nAttribute Additional Properties:\n-Attribute name: customerId\n-Key: Foreign\n-Display Name: Owner Customer ID\n-DataType: Integer\n-Required: Mandatory\n-Format: \"CUS-######\"\n-Values: N/A\n-Default: N/A\n-Validation: Foreign Key Check\n-Error Message: \"Customer ID must reference an existing customer\"\n-Description: References the customer who owns the collateral\n\nAttribute Additional Properties:\n-Attribute name: loanId\n-Key: Foreign\n-Display Name: Associated Loan ID\n-DataType: Integer\n-Required: Optional\n-Format: \"LN-######\"\n-Values: N/A\n-Default: N/A\n-Validation: Foreign Key Check\n-Error Message: \"Loan ID must reference an existing loan\"\n-Description: References the loan secured by this collateral\n\nAttribute Additional Properties:\n-Attribute name: collateralType\n-Key: Non-unique\n-Display Name: Collateral Type\n-DataType: Enum\n-Required: Mandatory\n-Format: N/A\n-Values: \"RealEstate\", \"Vehicle\", \"Investment\", \"Cash\", \"Other\"\n-Default: \"RealEstate\"\n-Validation: List Check\n-Error Message: \"Invalid collateral type\"\n-Description: Type of asset used as collateral\n\nAttribute Additional Properties:\n-Attribute name: description\n-Key: Non-unique\n-Display Name: Asset Description\n-DataType: String\n-Required: Mandatory\n-Format: N/A\n-Values: N/A\n-Default: N/A\n-Validation: Length Check\n-Error Message: \"Description is required\"\n-Description: Detailed description of the collateral\n\nAttribute Additional Properties:\n-Attribute name: value\n-Key: Non-unique\n-Display Name: Current Value\n-DataType: Decimal\n-Required: Mandatory\n-Format: \"$#,###.00\"\n-Values: 0.00-N\n-Default: N/A\n-Validation: Range Check\n-Error Message: \"Value must be greater than 0\"\n-Description: Current market value of the collateral\n\nAttribute Additional Properties:\n-Attribute name: appraisalDate\n-Key: Non-unique\n-Display Name: Appraisal Date\n-DataType: Date\n-Required: Mandatory\n-Format: \"YYYY-MM-DD\"\n-Values: N/A\n-Default: \"CURRENT_DATE\"\n-Validation: Date Check\n-Error Message: \"Appraisal date is required\"\n-Description: Date when the collateral was appraised\n\nAttribute Additional Properties:\n-Attribute name: appraisalValue\n-Key: Non-unique\n-Display Name: Appraisal Value\n-DataType: Decimal\n-Required: Mandatory\n-Format: \"$#,###.00\"\n-Values: 0.00-N\n-Default: N/A\n-Validation: Range Check\n-Error Message: \"Appraisal value must be greater than 0\"\n-Description: Value determined during appraisal\n\nAttribute Additional Properties:\n-Attribute name: lienStatus\n-Key: Non-unique\n-Display Name: Lien Status\n-DataType: Enum\n-Required: Mandatory\n-Format: N/A\n-Values: \"FirstLien\", \"SecondLien\", \"NoLien\"\n-Default: \"FirstLien\"\n-Validation: List Check\n-Error Message: \"Invalid lien status\"\n-Description: Status of liens against this collateral\n\nAttribute Additional Properties:\n-Attribute name: condition\n-Key: Non-unique\n-Display Name: Asset Condition\n-DataType: Enum\n-Required: Mandatory\n-Format: N/A\n-Values: \"Excellent\", \"Good\", \"Fair\", \"Poor\"\n-Default: \"Good\"\n-Validation: List Check\n-Error Message: \"Invalid condition\"\n-Description: Physical condition of the collateral\n\nAttribute Additional Properties:\n-Attribute name: assetAddress\n-Key: Non-unique\n-Display Name: Asset Street Address\n-DataType: String\n-Required: Optional\n-Format: N/A\n-Values: N/A\n-Default: N/A\n-Validation: Length Check\n-Error Message: N/A\n-Description: Address of the physical asset\n\nAttribute Additional Properties:\n-Attribute name: assetCity\n-Key: Non-unique\n-Display Name: Asset City\n-DataType: String\n-Required: Optional\n-Format: N/A\n-Values: N/A\n-Default: N/A\n-Validation: Length Check\n-Error Message: N/A\n-Description: City where the physical asset is located\n\nAttribute Additional Properties:\n-Attribute name: assetState\n-Key: Non-unique\n-Display Name: Asset State/Province\n-DataType: String\n-Required: Optional\n-Format: N/A\n-Values: N/A\n-Default: N/A\n-Validation: Length Check\n-Error Message: N/A\n-Description: State where the physical asset is located\n\nAttribute Additional Properties:\n-Attribute name: assetZipCode\n-Key: Non-unique\n-Display Name: Asset ZIP/Postal Code\n-DataType: String\n-Required: Optional\n-Format: \"#####\" or \"#####-####\"\n-Values: N/A\n-Default: N/A\n-Validation: Format Check\n-Error Message: \"Invalid zip code format\"\n-Description: Zip code where the physical asset is located\n\nAttribute Additional Properties:\n-Attribute name: insuranceProvider\n-Key: Non-unique\n-Display Name: Insurance Provider\n-DataType: String\n-Required: Optional\n-Format: N/A\n-Values: N/A\n-Default: N/A\n-Validation: Length Check\n-Error Message: N/A\n-Description: Insurance company covering the collateral\n\nAttribute Additional Properties:\n-Attribute name: insurancePolicyNumber\n-Key: Non-unique\n-Display Name: Insurance Policy Number\n-DataType: String\n-Required: Optional\n-Format: N/A\n-Values: N/A\n-Default: N/A\n-Validation: Length Check\n-Error Message: N/A\n-Description: Insurance policy number for the collateral\n\nAttribute Additional Properties:\n-Attribute name: insuranceExpirationDate\n-Key: Non-unique\n-Display Name: Insurance Expiration Date\n-DataType: Date\n-Required: Optional\n-Format: \"YYYY-MM-DD\"\n-Values: N/A\n-Default: N/A\n-Validation: Date Check\n-Error Message: \"Expiration date must be in the future\"\n-Description: Date when the insurance policy expires\n\nAttribute Additional Properties:\n-Attribute name: documentationComplete\n-Key: Non-unique\n-Display Name: Documentation Complete\n-DataType: Boolean\n-Required: Mandatory\n-Format: N/A\n-Values: \"true\", \"false\"\n-Default: \"false\"\n-Validation: Boolean Check\n-Error Message: \"Invalid documentation status\"\n-Description: Indicates if all required documentation is complete\n\nRelationship: Collateral to Customer\nRelationship Properties:\n-On Delete: Restrict (Prevent deletion of customer with active collateral)\n-On Update: Cascade (Update collateral records when customer details change)\n-Foreign Key Type: Non-Nullable\n\nRelationship: Collateral to Loan\nRelationship Properties:\n-On Delete: Set Null (Remove loan reference when loan is deleted)\n-On Update: Cascade (Update collateral records when loan details change)\n-Foreign Key Type: Nullable\n\nPayment has paymentId^PK, loanId^FK, amount, paymentDate, dueDate, status (Pending, Completed, Failed, Cancelled), paymentMethod (CreditCard, DebitCard, BankTransfer, Cash, Check, AutoDebit), referenceNumber, lateFee, paymentType (Regular, ExtraPayment, LatePayment, InterestOnly, PrincipalOnly), notes.\n\nRelationships for Payment:\n-Payment has many-to-one relationship with Loan using Payment.loanId to Loan.loanId^PK\n\nValidations for Payment:\n-Payment.paymentId must be unique\n-Payment.loanId must exist in Loan table\n-Payment.amount must be greater than 0\n-Payment.dueDate must be a valid date\n-Payment.paymentDate must be a valid date\n-Payment.status must be one of \"Pending\", \"Completed\", \"Failed\", \"Cancelled\"\n\nEntity Additional Properties:\n-Display Name: Loan Payment\n-Type: Core Entity\n-Description: Represents a payment made toward a loan\n\nAttribute Additional Properties:\n-Attribute name: paymentId\n-Key: Primary\n-Display Name: Payment ID\n-DataType: Integer\n-Required: Mandatory\n-Format: \"PMT-######\"\n-Values: N/A\n-Default: N/A\n-Validation: Auto-increment\n-Error Message: \"Invalid Payment ID format\"\n-Description: Unique identifier for the payment\n\nAttribute Additional Properties:\n-Attribute name: loanId\n-Key: Foreign\n-Display Name: Loan ID\n-DataType: Integer\n-Required: Mandatory\n-Format: \"LN-######\"\n-Values: N/A\n-Default: N/A\n-Validation: Foreign Key Check\n-Error Message: \"Loan ID must reference an existing loan\"\n-Description: References the loan this payment applies to\n\nAttribute Additional Properties:\n-Attribute name: amount\n-Key: Non-unique\n-Display Name: Payment Amount\n-DataType: Decimal\n-Required: Mandatory\n-Format: \"$#,###.00\"\n-Values: 0.01-N\n-Default: N/A\n-Validation: Range Check\n-Error Message: \"Payment amount must be greater than 0\"\n-Description: Amount of the payment\n\nAttribute Additional Properties:\n-Attribute name: paymentDate\n-Key: Non-unique\n-Display Name: Payment Date\n-DataType: Date\n-Required: Mandatory\n-Format: \"YYYY-MM-DD\"\n-Values: N/A\n-Default: \"CURRENT_DATE\"\n-Validation: Date Check\n-Error Message: \"Payment date is required\"\n-Description: Date when the payment was made\n\nAttribute Additional Properties:\n-Attribute name: dueDate\n-Key: Non-unique\n-Display Name: Due Date\n-DataType: Date\n-Required: Mandatory\n-Format: \"YYYY-MM-DD\"\n-Values: N/A\n-Default: N/A\n-Validation: Date Check\n-Error Message: \"Due date is required\"\n-Description: Date when the payment is due\n\nAttribute Additional Properties:\n-Attribute name: status\n-Key: Non-unique\n-Display Name: Payment Status\n-DataType: Enum\n-Required: Mandatory\n-Format: N/A\n-Values: \"Pending\", \"Completed\", \"Failed\", \"Cancelled\"\n-Default: \"Pending\"\n-Validation: List Check\n-Error Message: \"Invalid payment status\"\n-Description: Current status of the payment\n\nAttribute Additional Properties:\n-Attribute name: paymentMethod\n-Key: Non-unique\n-Display Name: Payment Method\n-DataType: Enum\n-Required: Mandatory\n-Format: N/A\n-Values: \"CreditCard\", \"DebitCard\", \"BankTransfer\", \"Cash\", \"Check\", \"AutoDebit\"\n-Default: \"AutoDebit\"\n-Validation: List Check\n-Error Message: \"Invalid payment method\"\n-Description: Method used for payment\n\nAttribute Additional Properties:\n-Attribute name: referenceNumber\n-Key: Non-unique\n-Display Name: Reference Number\n-DataType: String\n-Required: Optional\n-Format: N/A\n-Values: N/A\n-Default: N/A\n-Validation: Length Check\n-Error Message: N/A\n-Description: External reference number for the payment\n\nAttribute Additional Properties:\n-Attribute name: lateFee\n-Key: Non-unique\n-Display Name: Late Fee\n-DataType: Decimal\n-Required: Optional\n-Format: \"$#,###.00\"\n-Values: 0.00-N\n-Default: \"0.00\"\n-Validation: Range Check\n-Error Message: \"Late fee cannot be negative\"\n-Description: Additional fee applied for late payments\n\nAttribute Additional Properties:\n-Attribute name: paymentType\n-Key: Non-unique\n-Display Name: Payment Type\n-DataType: Enum\n-Required: Mandatory\n-Format: N/A\n-Values: \"Regular\", \"ExtraPayment\", \"LatePayment\", \"InterestOnly\", \"PrincipalOnly\"\n-Default: \"Regular\"\n-Validation: List Check\n-Error Message: \"Invalid payment type\"\n-Description: Type of payment being made\n\nAttribute Additional Properties:\n-Attribute name: notes\n-Key: Non-unique\n-Display Name: Payment Notes\n-DataType: String\n-Required: Optional\n-Format: N/A\n-Values: N/A\n-Default: N/A\n-Validation: Length Check\n-Error Message: N/A\n-Description: Additional notes about the payment\n\nRelationship: Payment to Loan\nRelationship Properties:\n-On Delete: Cascade (Remove payments when loan is deleted)\n-On Update: Cascade (Update payment records when loan details change)\n-Foreign Key Type: Non-Nullable", "type": "text"}]}, "url": {"raw": "{{baseurlCreationManual}}api/entities/validate", "host": ["{{baseurlCreationManual}}api"], "path": ["entities", "validate"]}}, "response": []}, {"name": "Workflow_go", "request": {"auth": {"type": "<PERSON><PERSON><PERSON>"}, "method": "POST", "header": [], "body": {"mode": "formdata", "formdata": [{"key": "text", "value": "## Leave Approval Process\n\nCore Metadata:\n- name: \"Process Leave Requests\"\n- version: \"1.0\"\n- status: \"Active\"\n- description: \"Manages employee leave requests from submission to approval or rejection\"\n- primary_entity: \"LeaveApplication\"\n- classification: \"workflow\"\n  - Global Objective: \"Leave Request Processing\"\n  - Book: \"Employee Leave Management\"\n  - Chapter: \"Leave Request Lifecycle\"\n  - Tenant: \"Acme Corporation\"\n\nProcess Ownership:\n- Originator: Employee\n- Process Owner: HR Manager\n- Business Sponsor: Human Resources Department\n\nTrigger Definition:\n- Trigger Type: user-initiated\n- Trigger Condition: Employee submits leave request\n- Trigger Schedule: on-demand\n- Trigger Attributes: LeaveApplication.employeeId, LeaveApplication.startDate, LeaveApplication.endDate, LeaveApplication.leaveTypeId\n\nLocal Objectives:\nLO-1: SubmitLeaveRequest [HUMAN]\nLO-2: UploadDocumentation [HUMAN]\nLO-3: ReviewLeaveRequest [HUMAN]\nLO-4: ApproveLeaveRequest [SYSTEM]\nLO-5: RejectLeaveRequest [SYSTEM]\nLO-6: NotifyEmployee [SYSTEM]\nLO-7: UpdateCalendar [SYSTEM]\nLO-8: UpdateLeaveBalance [SYSTEM]\nLO-9: LogAuditTrail [SYSTEM]\nLO-10: CancelLeaveRequest [SYSTEM]\nLO-11: RollbackLeaveApproval [SYSTEM]\nLO-12: RestoreLeaveBalance [SYSTEM]\n\nPathway Definitions:\nPATHWAY-1: Standard Approval (No Documentation)\nSTEPS: LO-1 → LO-3 → LO-4 → LO-6 → (LO-7, LO-9) → LO-8\n\nPATHWAY-2: Documentation Required Approval\nSTEPS: LO-1 → LO-2 → LO-3 → LO-4 → LO-6 → (LO-7, LO-9) → LO-8\n\nPATHWAY-3: Rejection Pathway\nSTEPS: LO-1 → LO-3 → LO-5 → LO-6 → (LO-7, LO-9)\n\nPathways:\n1.  SubmitLeaveRequest [HUMAN] \n    Description: Employee submits a leave request\n    Route Type: Alternate\n    a. If LeaveApplication.requiresDocumentation = true, route to UploadDocumentation\n    b. If LeaveApplication.requiresDocumentation = false, route to ReviewLeaveRequest\n2.  UploadDocumentation [HUMAN]\n    Description: Employee uploads required documentation\n    Route Type: Sequential\n    a. Route to ReviewLeaveRequest\n3.  ReviewLeaveRequest [HUMAN]\n    Description: Manager reviews the leave request\n    Route Type: Alternate\n    a. If LeaveApplication.status = \"Approved\", route to ApproveLeaveRequest\n    b. If LeaveApplication.status = \"Rejected\", route to RejectLeaveRequest\n4.  ApproveLeaveRequest [SYSTEM]\n    Description: System updates leave request status to approved\n    Route Type: Sequential\n    a. Route to NotifyEmployee\n5.  RejectLeaveRequest [SYSTEM]\n    Description: System updates leave request status to rejected\n    Route Type: Sequential\n    a. Route to NotifyEmployee\n6.  NotifyEmployee [SYSTEM]\n    Description: System notifies employee of the decision\n    Route Type: Parallel\n    a. Route to UpdateCalendar - System updates employee calendar\n    b. Route to LogAuditTrail - System logs audit trail for compliance\n    c. Join at: UpdateLeaveBalance\n7.  UpdateCalendar [SYSTEM]\n    Description: System updates employee calendar\n    Route Type: Alternate\n    a. If LeaveApplication.status = \"Approved\", route to UpdateLeaveBalance\n    b. If LeaveApplication.status = \"Rejected\", route to LogAuditTrail\n8.  UpdateLeaveBalance [SYSTEM]\n    Description: System updates employee leave balance\n    Route Type: Sequential\n    a. Complete process\n9.  LogAuditTrail [SYSTEM]\n    Description: System logs audit trail for compliance\n    Route Type: Sequential\n    a. Route to UpdateLeaveBalance\n\nBusinessRule for LeaveApplication:\nvalidate_leave_request_advance_notice_1\nInputs: LeaveApplication with startDate, leaveTypeId; LeaveType with typeId, name\nOperation: conditional_logic({\"condition\": \"AND\", \"rules\": [{\"field\": \"LeaveType.name\", \"operator\": \"notEquals\", \"value\": \"Sick Leave\"}, {\"field\": \"LeaveApplication.startDate\", \"operator\": \"lessThan\", \"value\": \"CURRENT_DATE + 7\"}]}, {\"result\": \"error\", \"message\": \"Leave requests must be submitted at least 7 days in advance for planned leave\"}, {\"result\": \"success\"}, LeaveApplication.validationResult)\nDescription: Validates that planned leave requests (non-sick leave) are submitted at least 7 days in advance.\nOutput: LeaveApplication with validationResult\nError: When a planned leave request is submitted less than 7 days in advance, returns \"Leave requests must be submitted at least 7 days in advance for planned leave\"\nValidation: PRE_INSERT on LeaveApplication entity\n\nvalidate_sick_leave_retroactive_1\nInputs: LeaveApplication with startDate, leaveTypeId; LeaveType with typeId, name\nOperation: conditional_logic({\"condition\": \"AND\", \"rules\": [{\"field\": \"LeaveType.name\", \"operator\": \"equals\", \"value\": \"Sick Leave\"}, {\"field\": \"LeaveApplication.startDate\", \"operator\": \"lessThan\", \"value\": \"CURRENT_DATE - 3\"}]}, {\"result\": \"error\", \"message\": \"Sick leave can be submitted retroactively within 3 days\"}, {\"result\": \"success\"}, LeaveApplication.validationResult)\nDescription: Validates that sick leave can be submitted retroactively but only within 3 days.\nOutput: LeaveApplication with validationResult\nError: When sick leave is submitted more than 3 days after the start date, returns \"Sick leave can be submitted retroactively within 3 days\"\nValidation: PRE_INSERT on LeaveApplication entity\n\nvalidate_leave_documentation_requirement_1\nInputs: LeaveApplication with leaveTypeId, numDays; LeaveType with typeId, name, requiresDocumentation\nOperation: conditional_logic({\"condition\": \"AND\", \"rules\": [{\"field\": \"LeaveType.name\", \"operator\": \"equals\", \"value\": \"Sick Leave\"}, {\"field\": \"LeaveApplication.numDays\", \"operator\": \"greaterThan\", \"value\": 3}, {\"field\": \"LeaveApplication.documentationProvided\", \"operator\": \"equals\", \"value\": false}]}, {\"result\": \"error\", \"message\": \"Documentation is required for sick leave exceeding 3 days\"}, {\"result\": \"success\"}, LeaveApplication.validationResult)\nDescription: Validates that documentation is provided for sick leave requests exceeding 3 days.\nOutput: LeaveApplication with validationResult\nError: When documentation is not provided for sick leave exceeding 3 days, returns \"Documentation is required for sick leave exceeding 3 days\"\nValidation: PRE_INSERT on LeaveApplication entity\n\nvalidate_leave_cancellation_notice_1\nInputs: LeaveApplication with leaveId, startDate, status\nOperation: conditional_logic({\"condition\": \"AND\", \"rules\": [{\"field\": \"FUNCTION_TYPE\", \"operator\": \"equals\", \"value\": \"update\"}, {\"field\": \"LeaveApplication.status\", \"operator\": \"changedTo\", \"value\": \"Cancelled\"}, {\"field\": \"LeaveApplication.startDate\", \"operator\": \"lessThan\", \"value\": \"CURRENT_DATE + 1\"}]}, {\"result\": \"error\", \"message\": \"Cancellations must be made at least 24 hours in advance\"}, {\"result\": \"success\"}, LeaveApplication.validationResult)\nDescription: Validates that leave cancellations are made at least 24 hours in advance of the leave start date.\nOutput: LeaveApplication with validationResult\nError: When a leave request is cancelled less than 24 hours before the start date, returns \"Cancellations must be made at least 24 hours in advance\"\nValidation: PRE_UPDATE on LeaveApplication entity\n\nvalidate_manager_approval_for_long_leave_1\nInputs: LeaveApplication with numDays, managerApproval\nOperation: conditional_logic({\"condition\": \"AND\", \"rules\": [{\"field\": \"LeaveApplication.numDays\", \"operator\": \"greaterThan\", \"value\": 5}, {\"field\": \"LeaveApplication.managerApproval\", \"operator\": \"equals\", \"value\": false}]}, {\"result\": \"error\", \"message\": \"Leave requests exceeding 5 consecutive days require manager approval\"}, {\"result\": \"success\"}, LeaveApplication.validationResult)\nDescription: Validates that leave requests exceeding 5 consecutive days have manager approval.\nOutput: LeaveApplication with validationResult\nError: When a leave request exceeding 5 days does not have manager approval, returns \"Leave requests exceeding 5 consecutive days require manager approval\"\nValidation: PRE_UPDATE on LeaveApplication entity where status changed to \"Approved\"\n\nPerformance Metadata:\n- cycle_time: \"2 business days\"\n- number_of_pathways: 4\n- volume_metrics:\n  * average_volume: 120\n  * peak_volume: 250\n  * unit: \"requests/month\"\n- sla_thresholds:\n  * manager_review: \"1 business day\"\n  * notification: \"1 hour\"\n  * system_processing: \"5 minutes\"\n- critical_lo_performance:\n  * \"ReviewLeaveRequest\": \"24 hours maximum\"\n  * \"NotifyEmployee\": \"1 hour maximum\"\n  * \"UpdateCalendar\": \"5 minutes maximum\"\n  * \"UpdateLeaveBalance\": \"5 minutes maximum\"\n\nProcess Mining Schema:\n\nEvent Log Specification:\n- case_id: \"LeaveApplication.leaveId\"\n- activity: \"LO_name\"\n- event_type: \"start/complete/abort/rollback\"\n- timestamp: \"ISO-8601 datetime\"\n- resource: \"role/system_executing\"\n- duration: \"milliseconds\"\n- attributes:\n  * entity_state: \"json_snapshot\"\n  * input_values: \"input_parameters\"\n  * output_values: \"output_results\"\n  * execution_status: \"success/failure/pending\"\n  * error_details: \"error_message_if_any\"\n\nPerformance Discovery Metrics:\n- pathway_frequency:\n  * \"Standard Approval\":\n    - frequency: 95\n    - percentage: 79\n    - average_duration: \"1.5 days\"\n    - success_rate: 98\n  * \"Documentation Required\":\n    - frequency: 25\n    - percentage: 21\n    - average_duration: \"2.8 days\"\n    - success_rate: 92\n- bottleneck_analysis:\n  * \"ReviewLeaveRequest\":\n    - average_wait_time: \"1.2 days\"\n    - queue_length: 8\n    - resource_utilization: 75\n    - failure_rate: 5\n- resource_patterns:\n  * \"HR Manager\":\n    - active_hours: \"9:00-17:00 business days\"\n    - peak_load_periods: \"Monday mornings, month-end\"\n    - concurrent_executions: 12\n  * \"Notification System\":\n    - active_hours: \"24/7\"\n    - peak_load_periods: \"8:00-10:00 workdays\"\n    - concurrent_executions: 50\n\nConformance Analytics:\n- compliance_rate: 96\n- execution_variance:\n  * \"ReviewLeaveRequest\":\n    - expected_duration: \"4 hours\"\n    - actual_duration_range: \"2-24 hours\"\n    - variance_causes: [\"manager availability\", \"high request volume\", \"incomplete documentation\"]\n  * \"NotifyEmployee\":\n    - expected_duration: \"10 minutes\"\n    - actual_duration_range: \"5-25 minutes\"\n    - variance_causes: [\"notification system latency\", \"email delivery issues\"]\n- exception_patterns:\n  * \"input_timeout\":\n    - frequency: 6\n    - affected_pathways: [\"Documentation Required\"]\n    - recovery_success_rate: 85\n  * \"validation_failure\":\n    - frequency: 12\n    - most_common_failures: [\"insufficient leave balance\", \"invalid date range\"]\n    - resolution_time: \"1.5 days\"\n  * \"system_error\":\n    - frequency: 2\n    - error_categories: [\"calendar integration\", \"notification delivery\"]\n    - automatic_recovery_rate: 92\n\nAdvanced Process Intelligence:\n- process_health_score:\n  * performance_score: 88\n  * compliance_score: 96\n  * efficiency_score: 84\n  * overall_health: 89\n- prediction_models:\n  * completion_time_forecast:\n    - algorithm: \"Random Forest Regression\"\n    - accuracy: 87\n    - confidence_interval: \"±0.4 days\"\n  * failure_prediction:\n    - algorithm: \"Gradient Boosting Classification\"\n    - precision: 82\n    - recall: 79\n- optimization_insights:\n  * bottleneck_elimination: [\"Implement auto-approval for standard requests under 2 days\", \"Expand manager delegation options\"]\n  * resource_reallocation: [\"Add review capacity during Monday mornings\", \"Implement notification batching\"]\n  * pathway_optimization: [\"Streamline documentation review process\", \"Implement one-click approvals for recurring requests\"]\n\nRollback Analytics:\n- rollback_frequency: 4\n- rollback_success_rate: 99\n- rollback_triggers:\n  * \"user_initiated\":\n    - frequency: 18\n    - average_impact_scope: 2\n    - average_recovery_time: \"2 hours\"\n  * \"system_error\":\n    - frequency: 2\n    - average_impact_scope: 1\n    - average_recovery_time: \"1 hour\"\n- rollback_pathways:\n  * \"reject_leave_request\":\n    - frequency: 16\n    - success_rate: 99\n    - average_completion_time: \"2.5 hours\"\n  * \"update_leave_balance\":\n    - frequency: 4\n    - success_rate: 95\n    - average_completion_time: \"1.2 hours\"\n    \nValidation Rules for GO Creation:\n\nvalidate_lo_natural_language_names\nInputs: GO with pathways\nOperation: validate_all(GO.pathways, \"name\", matches_pattern(\"[A-Z][a-zA-Z]+\"))\nDescription: Validates that all LOs are defined with natural language names\nOutput: ValidationResult with status, message\nError: When LO names don't follow natural language pattern, returns \"All LOs must be defined with natural language names\"\nValidation: PRE_DEPLOY on GO entity\n\nvalidate_referenced_los_defined\nInputs: GO with pathways, routings\nOperation: validate_references(GO.routings, \"target\", exists_in(GO.pathways, \"name\"))\nDescription: Validates that every referenced LO is defined in Process Flow section\nOutput: ValidationResult with status, message\nError: When routing references undefined LO, returns \"All referenced LOs must be defined in Process Flow section\"\nValidation: PRE_DEPLOY on GO entity\n\nvalidate_first_lo_human_initiated\nInputs: GO with pathways\nOperation: validate_equals(GO.pathways[0].actor_type, \"HUMAN\")\nDescription: Validates that the first LO is human-initiated\nOutput: ValidationResult with status, message\nError: When first LO is not human-initiated, returns \"First LO must be human-initiated\"\nValidation: PRE_DEPLOY on GO entity\n\nvalidate_terminal_pathways\nInputs: GO with pathways, routings\nOperation: validate_all_paths_terminate(GO.pathways, GO.routings)\nDescription: Validates that all pathways terminate in completion or cancellation\nOutput: ValidationResult with status, message\nError: When pathway doesn't terminate, returns \"All pathways must terminate in completion or cancellation\"\nValidation: PRE_DEPLOY on GO entity\n\nvalidate_rollback_pathways\nInputs: GO with pathways, rollback_pathways\nOperation: validate_all_exist(GO.rollback_pathways, GO.pathways, \"name\")\nDescription: Validates that rollback LOs are included in Process Flow\nOutput: ValidationResult with status, message\nError: When rollback pathway references undefined LO, returns \"All rollback LOs must be included in Process Flow\"\nValidation: PRE_DEPLOY on GO entity\n\nvalidate_parallel_flows\nInputs: GO with pathways, parallel_flows\nOperation: validate_all_exist(GO.parallel_flows, GO.pathways, \"name\")\nDescription: Validates that parallel flows reference defined LOs\nOutput: ValidationResult with status, message\nError: When parallel flow references undefined LO, returns \"All parallel flows must reference defined LOs\"\nValidation: PRE_DEPLOY on GO entity\n\nvalidate_performance_metadata\nInputs: GO with performance_metadata\nOperation: validate_required_fields(GO.performance_metadata, [\"cycle_time\", \"number_of_pathways\", \"volume_metrics\", \"sla_thresholds\", \"critical_lo_performance\"])\nDescription: Validates that all required performance metadata is specified\nOutput: ValidationResult with status, message\nError: When required performance metadata is missing, returns \"All required performance metadata must be specified\"\nValidation: PRE_DEPLOY on GO entity", "type": "text"}]}, "url": {"raw": "{{baseurlCreationManual}}api/workflows/validate", "host": ["{{baseurlCreationManual}}api"], "path": ["workflows", "validate"]}}, "response": []}, {"name": "Workflow_lo", "request": {"auth": {"type": "<PERSON><PERSON><PERSON>"}, "method": "POST", "header": [], "body": {"mode": "formdata", "formdata": [{"key": "text", "value": "SubmitLeaveRequest\n\nname: \"Submit Leave Request\"\nversion: \"1.0\"\nstatus: \"Active\"\nworkflow_source: \"origin\"\nfunction_type: \"Create\"\nagent_type: \"HUMAN\"\nui_type: \"Form\"\nexecution_rights: \"Employee\"\n\nInputs: LeaveApplication with leaveId*, employeeId*, startDate*, endDate*, numDays*, reason*, leaveTypeName* (Annual Leave, Sick Leave, Personal Leave), leaveSubTypeName* [dependent on leaveTypeName], requiresDocumentation* (true, false), status* (Pending, Approved, Rejected), instructions [information]*, allowedNumberOfDays [constant]*\n\nLeaveApplication.leaveId\n    - Source: nested_function\n    - Agent: DIGITAL\n    - Function: generate_id_1\n    - Attribute type: system_generated\n\nLeaveApplication.employeeId\n    - Source: user\n    - Agent: HUMAN\n    - Attribute type: input\n\nLeaveApplication.startDate\n    - Source: user\n    - Agent: HUMAN\n    - Attribute type: input\n\nLeaveApplication.endDate\n    - Source: user\n    - Agent: HUMAN\n    - Attribute type: input\n\nLeaveApplication.numDays\n    - Source: nested_function\n    - Agent: DIGITAL\n    - Function Reference: subtract_days_1\n    - Attribute type: system_dependent\n\nLeaveApplication.reason\n    - Source: user\n    - Agent: HUMAN\n    - Attribute type: input\n\nLeaveApplication.leaveTypeName\n    - Source: user\n    - Agent: HUMAN\n    - Attribute type: input\n\nLeaveApplication.leaveSubTypeName\n    - Source: nested_function\n    - Agent: HUMAN\n    - Function Reference: fetch_records_1\n    - Attribute type: system_dependent\n\nLeaveApplication.requiresDocumentation\n    - Source: user\n    - Agent: HUMAN\n    - Attribute type: input\n\nLeaveApplication.status\n    - Source: default_value\n    - Agent: DIGITAL\n    - Default: \"Pending\"\n    - Attribute type: information\n\nLeaveApplication.instructions\n    - Source: nested_function\n    - Agent: DIGITAL\n    - Default: \"Please ensure all required documents are attached before submitting your leave application. For leaves longer than 3 days, documentation is mandatory.\"\n    - Function Reference: fetch_records_2\n    - Attribute type: information\n\nLeaveApplication.allowedNumberOfDays\n    - Source: nested_function\n    - Agent: DIGITAL\n    - Function Reference: fetch_records_3\n    - Attribute type: constant\n\nOutputs: LeaveApplication with leaveId, employeeId, startDate, endDate, numDays, reason, leaveTypeName, leaveSubTypeName, requiresDocumentation, status, submissionDate, allowedNumberOfDays*\n\n\nValidation Stack\n\n- Attribute: LeaveApplication.leaveId\n- Validation Function: validate_unique_string\n- Success Value: valid_unique_id\n- Failure Value: null\n- Success Message: \"Leave ID generated successfully\"\n- Failure Message: \"Leave ID is required and must be unique\"\n\n- Attribute: LeaveApplication.employeeId\n- Validation Function: validate_employee_exists\n- Success Value: existing_employee_id\n- Failure Value: null\n- Success Message: \"Employee ID validated successfully\"\n- Failure Message: \"Employee ID is required\"\n\nLeaveApplication.startDate\n- Attribute: LeaveApplication.startDate\n- Validation Function: validate_date_format\n- Success Value: valid_date\n- Failure Value: null\n- Success Message: \"Start date is valid\"\n- Failure Message: \"Start date is required\"\n\nLeaveApplication.endDate\n- Attribute: LeaveApplication.endDate\n- Validation Function: validate_date_after_start\n- Success Value: valid_end_date\n- Failure Value: null\n- Success Message: \"End date is valid and after start date\"\n- Failure Message: \"End date must be after start date\"\n\nLeaveApplication.numDays\n- Attribute: LeaveApplication.numDays\n- Validation Function: validate_positive_number\n- Success Value: positive_number\n- Failure Value: 0\n- Success Message: \"Number of days calculated correctly\"\n- Failure Message: \"Number of days must be greater than 0\"\n\nLeaveApplication.reason\n- Attribute: LeaveApplication.reason\n- Validation Function: validate_text_length_min\n- Success Value: valid_text\n- Failure Value: empty_string\n- Success Message: \"Reason provided is valid\"\n- Failure Message: \"Please provide a detailed reason for your leave request\"\n\nLeaveApplication.leaveTypeName\n- Attribute: LeaveApplication.leaveTypeName\n- Validation Function: validate_enum_value\n- Success Value: valid_leave_type\n- Failure Value: null\n- Success Message: \"Leave type is valid\"\n- Failure Message: \"Please select a valid leave type\"\n\nLeaveApplication.leaveSubTypeName\n- Attribute: LeaveApplication.leaveSubTypeName\n- Validation Function: validate_subtype_for_type\n- Success Value: valid_subtype\n- Failure Value: null\n- Success Message: \"Leave subtype matches leave type\"\n- Failure Message: \"Please select a valid leave sub-type\"\n\nLeaveApplication.allowedNumberOfDays\n- Attribute: LeaveApplication.allowedNumberOfDays\n- Validation Function: validate_positive_number\n- Success Value: positive_number\n- Failure Value: 0\n- Success Message: \"Allowed days validated\"\n- Failure Message: \"Invalid allowed days configuration\"\n\nLeaveApplication.status\n- Attribute: LeaveApplication.status\n- Validation Function: validate_enum_value\n- Success Value: valid_status\n- Failure Value: \"Pending\"\n- Success Message: \"Status set successfully\"\n- Failure Message: \"Invalid status value\"\n\nUI Stack\n\nLeaveApplication.leaveId\n- UI Control: label\n- Data Type: text\n- Editable: false\n- Required: true\n- Hidden: false\n\nLeaveApplication.employeeId\n- UI Control: label\n- Data Type: text\n- Editable: false\n- Required: true\n- Hidden: false\n\nLeaveApplication.startDate\n- UI Control: date\n- Data Type: date\n- Editable: true\n- Required: true\n- Hidden: false\n\nLeaveApplication.endDate\n- UI Control: date\n- Data Type: date\n- Editable: true\n- Required: true\n- Hidden: false\n\nLeaveApplication.numDays\n- UI Control: label\n- Data Type: integer\n- Editable: false\n- Required: true\n- Hidden: false\n\nLeaveApplication.reason\n- UI Control: multiline\n- Data Type: text\n- Editable: true\n- Required: true\n- Hidden: false\n\nLeaveApplication.leaveTypeName\n- UI Control: dropdown\n- Data Type: text\n- Editable: true\n- Required: true\n- Hidden: false\n\nLeaveApplication.leaveSubTypeName\n- UI Control: dropdown\n- Data Type: text\n- Editable: true\n- Required: true\n- Hidden: false\n\nLeaveApplication.requiresDocumentation\n- UI Control: boolean\n- Data Type: boolean\n- Editable: false\n- Required: true\n- Hidden: false\n\nLeaveApplication.instructions\n- UI Control: label\n- Data Type: text\n- Editable: false\n- Required: false\n- Hidden: false\n\nLeaveApplication.allowedNumberOfDays\n- UI Control: label\n- Data Type: integer\n- Editable: false\n- Required: true\n- Hidden: false\n\nMapping Stack:\n* SubmitLeaveRequest.leaveId.output maps to UploadDocumentation.leaveId.input\n* SubmitLeaveRequest.employeeId.output maps to UploadDocumentation.employeeId.input\n* SubmitLeaveRequest.leaveTypeName.output maps to UploadDocumentation.leaveTypeName.input\n* SubmitLeaveRequest.leaveSubTypeName.output maps to UploadDocumentation.leaveSubTypeName.input\n* SubmitLeaveRequest.requiresDocumentation.output maps to UploadDocumentation.requiresDocumentation.input\n* SubmitLeaveRequest.leaveId.output maps to ReviewLeaveRequest.leaveId.input\n* SubmitLeaveRequest.employeeId.output maps to ReviewLeaveRequest.employeeId.input\n* SubmitLeaveRequest.startDate.output maps to ReviewLeaveRequest.startDate.input\n* SubmitLeaveRequest.endDate.output maps to ReviewLeaveRequest.endDate.input\n* SubmitLeaveRequest.numDays.output maps to ReviewLeaveRequest.numDays.input\n* SubmitLeaveRequest.reason.output maps to ReviewLeaveRequest.reason.input\n* SubmitLeaveRequest.leaveTypeName.output maps to ReviewLeaveRequest.leaveTypeName.input\n* SubmitLeaveRequest.leaveSubTypeName.output maps to ReviewLeaveRequest.leaveSubTypeName.input\n* SubmitLeaveRequest.allowedNumberOfDays.output maps to ReviewLeaveRequest.allowedNumberOfDays.input\n\nNested Function Stack\n\nFunction: generate_id_1\n    - Source Attribute: LeaveApplication.leaveId\n    - Function Name: generate_id\n    - Inputs: [entity='LeaveApplication',attribute='LeaveApplication.leaveId',prefix=\"LE\"]\n    - Conditions: []\n    - Outputs: [LeaveApplication.leaveId]\n\nFunction: subtract_days_1\n    - Source Attribute: LeaveApplication.numDays\n    - Function Name: subtract_days\n    - Inputs: [LeaveApplication.startDate, LeaveApplication.endDate]\n    - Conditions: []\n    - Outputs: [LeaveApplication.numDays]\n\nFunction: fetch_records_1\n    - Source Attribute: LeaveApplication.leaveSubTypeName\n    - Function Name: fetch_records\n    - Inputs: [entity='LeaveSubType', filters={'leaveTypeName': 'LeaveApplication.leaveTypeName'}]\n    - Conditions: [LeaveSubType.leaveTypeName = LeaveApplication.leaveTypeName]\n    - Outputs: [LeaveApplication.leaveSubTypeName]\n\nFunction: fetch_records_2\n    - Source Attribute: LeaveApplication.instructions\n    - Function Name: fetch_records\n    - Inputs: [entity='PolicyInstructions', filters={'leaveTypeName': 'LeaveApplication.leaveTypeName', 'leaveSubTypeName': 'LeaveApplication.leaveSubTypeName'}]\n    - Conditions: []\n    - Outputs: [LeaveApplication.instructions]\n\nFunction: fetch_records_3\n    - Source Attribute: LeaveApplication.allowedNumberOfDays\n    - Function Name: fetch_records\n    - Inputs: [entity='Constants', filters={'entityName': 'LeaveApplication', 'attribute': 'allowedNumberOfDays'}]\n    - Conditions: []\n    - Outputs: [LeaveApplication.allowedNumberOfDays]\n\nFunction: current_timestamp_1\n    - Source Attribute: LeaveApplication.submissionDate\n    - Function Name: current_timestamp\n    - Inputs: []\n    - Conditions: []\n    - Outputs: [LeaveApplication.submissionDate]\n\nFunction: format_enum_value_1\n    - Source Attribute: LeaveApplication.leaveTypeName\n    - Function Name: format_enum_value\n    - Inputs: [value='LeaveApplication.leaveTypeName']\n    - Conditions: []\n    - Outputs: [LeaveApplication.leaveTypeName]\n\nFunction: conditional_assignment_1\n    - Source Attribute: LeaveApplication.requiresDocumentation\n    - Function Name: conditional_assignment\n    - Inputs: [left_operand=\"LeaveApplication.leaveTypeName\", operator=\"equals\", right_operand=\"Sick Leave\", true_value=true, false_value=false, left_operand2=\"LeaveApplication.numDays\", operator2=\"greater_than\", right_operand2=3, logic_operator=\"AND\"]\n    - Conditions: [LeaveApplication.leaveTypeName = \"Sick Leave\" AND LeaveApplication.numDays > 3]\n    - Outputs: [LeaveApplication.requiresDocumentation]\n\nInput Mapping:\n- LeaveApplication.employeeId -> users.Id\n\nExecution pathway:\n* When LeaveApplication.requiresDocumentation = true, route to UploadDocumentation.\n* When LeaveApplication.requiresDocumentation = false, route to ReviewLeaveRequest.\n", "type": "text"}]}, "url": {"raw": "{{baseurlCreationManual}}api/workflows/validate", "host": ["{{baseurlCreationManual}}api"], "path": ["workflows", "validate"]}}, "response": []}]}, {"name": "health", "request": {"method": "GET", "header": [], "url": {"raw": "http://**********:8000/api/v2/health", "protocol": "http", "host": ["10", "26", "1", "52"], "port": "8000", "path": ["api", "v2", "health"]}}, "response": []}, {"name": "register", "request": {"method": "POST", "header": [], "body": {"mode": "raw", "raw": "{\"username\": \"testuser12\", \"email\": \"<EMAIL>\", \"password\": \"test@123\", \"first_name\": \"<PERSON><PERSON>\", \"last_name\": \"<PERSON>\", \"roles\": [\"User\"], \"tenant_id\": \"t001\"}", "options": {"raw": {"language": "json"}}}, "url": {"raw": "http://**********:8000/api/v2/auth/register", "protocol": "http", "host": ["10", "26", "1", "52"], "port": "8000", "path": ["api", "v2", "auth", "register"]}}, "response": []}, {"name": "getUserDetailsbyId", "protocolProfileBehavior": {"disableBodyPruning": true}, "request": {"method": "GET", "header": [], "body": {"mode": "raw", "raw": "{\"username\": \"dp12\", \"email\": \"<EMAIL>\", \"password\": \"secure123\", \"first_name\": \"New\", \"last_name\": \"User\", \"roles\": [\"R1\"], \"tenant_id\": \"t001\"}", "options": {"raw": {"language": "json"}}}, "url": {"raw": "http://**********:8000/api/v2/auth/user/U14", "protocol": "http", "host": ["10", "26", "1", "52"], "port": "8000", "path": ["api", "v2", "auth", "user", "U14"]}}, "response": []}, {"name": "profile", "protocolProfileBehavior": {"disableBodyPruning": true}, "request": {"method": "GET", "header": [], "body": {"mode": "raw", "raw": "{\"username\": \"dp12\", \"email\": \"<EMAIL>\", \"password\": \"secure123\", \"first_name\": \"New\", \"last_name\": \"User\", \"roles\": [\"R1\"], \"tenant_id\": \"t001\"}", "options": {"raw": {"language": "json"}}}, "url": {"raw": "http://**********:8000/api/v2/auth/profile/U14", "protocol": "http", "host": ["10", "26", "1", "52"], "port": "8000", "path": ["api", "v2", "auth", "profile", "U14"]}}, "response": []}, {"name": "departments", "protocolProfileBehavior": {"disableBodyPruning": true}, "request": {"method": "GET", "header": [], "body": {"mode": "raw", "raw": "{\"username\": \"dp12\", \"email\": \"<EMAIL>\", \"password\": \"secure123\", \"first_name\": \"New\", \"last_name\": \"User\", \"roles\": [\"R1\"], \"tenant_id\": \"t001\"}", "options": {"raw": {"language": "json"}}}, "url": {"raw": "{{baseurl}}api/v2/auth/departments", "host": ["{{baseurl}}api"], "path": ["v2", "auth", "departments"]}}, "response": []}, {"name": "teams", "protocolProfileBehavior": {"disableBodyPruning": true}, "request": {"method": "GET", "header": [], "body": {"mode": "raw", "raw": "{\"username\": \"dp12\", \"email\": \"<EMAIL>\", \"password\": \"secure123\", \"first_name\": \"New\", \"last_name\": \"User\", \"roles\": [\"R1\"], \"tenant_id\": \"t001\"}", "options": {"raw": {"language": "json"}}}, "url": {"raw": "{{baseurl}}api/v2/auth/teams", "host": ["{{baseurl}}api"], "path": ["v2", "auth", "teams"]}}, "response": []}, {"name": "login", "request": {"method": "POST", "header": [], "body": {"mode": "raw", "raw": "// {\"username\": \"dp12\", \"password\": \"secure123\"}\r\n{\"username\": \"testuser12\", \"password\": \"test@123\"}", "options": {"raw": {"language": "json"}}}, "url": {"raw": "http://**********:8000/api/v2/auth/login", "protocol": "http", "host": ["10", "26", "1", "52"], "port": "8000", "path": ["api", "v2", "auth", "login"]}}, "response": []}, {"name": "login with email", "request": {"method": "POST", "header": [], "body": {"mode": "raw", "raw": "{\"username\": \"<EMAIL>\", \"password\": \"secure123\"}", "options": {"raw": {"language": "json"}}}, "url": {"raw": "http://**********:8000/api/v2/auth/login", "protocol": "http", "host": ["10", "26", "1", "52"], "port": "8000", "path": ["api", "v2", "auth", "login"]}}, "response": []}, {"name": "refresh", "request": {"method": "POST", "header": [], "body": {"mode": "raw", "raw": "{\"refresh_token\": \"eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJzdWIiOiJVMTQiLCJleHAiOjE3NDg4NjAxODV9.OFqBj7cK33E6jNPCb-t1bEGWlnurpxLZrM9yAswdmoc\"}", "options": {"raw": {"language": "json"}}}, "url": {"raw": "http://**********:8000/api/v2/auth/refresh", "protocol": "http", "host": ["10", "26", "1", "52"], "port": "8000", "path": ["api", "v2", "auth", "refresh"]}}, "response": []}, {"name": "logout", "request": {"method": "POST", "header": [], "body": {"mode": "raw", "raw": "{\"refresh_token\": \"eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJzdWIiOiJVMTQiLCJleHAiOjE3NDg4NjAxODV9.OFqBj7cK33E6jNPCb-t1bEGWlnurpxLZrM9yAswdmoc\"}", "options": {"raw": {"language": "json"}}}, "url": {"raw": "http://**********:8000/api/v2/auth/logout", "protocol": "http", "host": ["10", "26", "1", "52"], "port": "8000", "path": ["api", "v2", "auth", "logout"]}}, "response": []}, {"name": "permissions_check", "request": {"method": "POST", "header": [], "body": {"mode": "raw", "raw": "{\"user_id\": \"U14\", \"permission_type\": \"local_objective\", \"resource_identifier\": \"GO1.LO1\", \"action\": \"create\"}", "options": {"raw": {"language": "json"}}}, "url": {"raw": "{{baseurl}}api/v2/auth/permissions/check", "host": ["{{baseurl}}api"], "path": ["v2", "auth", "permissions", "check"]}}, "response": []}]}, {"name": "message", "request": {"method": "POST", "header": [], "body": {"mode": "raw", "raw": "{  \"message\": \"asdfdsfn\"}", "options": {"raw": {"language": "json"}}}, "url": {"raw": "http://**********:8040/conversation/message", "protocol": "http", "host": ["10", "26", "1", "52"], "port": "8040", "path": ["conversation", "message"]}}, "response": []}, {"name": "login", "request": {"method": "POST", "header": [], "body": {"mode": "formdata", "formdata": [{"key": "username", "value": "<EMAIL>", "type": "text"}, {"key": "password", "value": "secure123", "type": "text"}]}, "url": {"raw": "http://**********:8000/api/v1/auth/token", "protocol": "http", "host": ["10", "26", "1", "52"], "port": "8000", "path": ["api", "v1", "auth", "token"]}}, "response": []}, {"name": "conversation_new", "request": {"method": "POST", "header": [], "body": {"mode": "raw", "raw": "", "options": {"raw": {"language": "json"}}}, "url": {"raw": "http://**********:8003/conversation/new", "protocol": "http", "host": ["10", "26", "1", "52"], "port": "8003", "path": ["conversation", "new"]}}, "response": []}, {"name": "conversation_new", "request": {"method": "POST", "header": [], "body": {"mode": "raw", "raw": "{\"user_input\" : \"yes, that's correct.\",\"session_id\" : \"67a1c7f8-2e95-4cba-9bda-1d97e3eef0c0\"}\r\n// {\"user_input\" : \"create a multi-tenant e-commerce paltform where each tenant can have their own //branded storefront, product catalog.\",\"session_id\" : \"67a1c7f8-2e95-4cba-9bda-1d97e3eef0c0\"}", "options": {"raw": {"language": "json"}}}, "url": {"raw": "http://**********:8003/conversation", "protocol": "http", "host": ["10", "26", "1", "52"], "port": "8003", "path": ["conversation"]}}, "response": []}, {"name": "conversation_new Copy", "request": {"method": "GET", "header": [], "url": {"raw": "http://**********:8003/conversations", "protocol": "http", "host": ["10", "26", "1", "52"], "port": "8003", "path": ["conversations"]}}, "response": []}, {"name": "New Request", "request": {"method": "POST", "header": [], "body": {"mode": "raw", "raw": "{\"user_input\" : \"yes thats correct\",\"session_id\" : \"c1b41a88-3814-4d38-a34b-c43404a8b35d\"}", "options": {"raw": {"language": "json"}}}, "url": {"raw": "http://**********:8003/conversation", "protocol": "http", "host": ["10", "26", "1", "52"], "port": "8003", "path": ["conversation"]}}, "response": []}]}