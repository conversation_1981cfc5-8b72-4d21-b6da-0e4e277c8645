{"info": {"_postman_id": "email-api-collection-001", "name": "NSL Email API Collection", "description": "Complete email system with SMTP integration for NSL Platform", "schema": "https://schema.getpostman.com/json/collection/v2.1.0/collection.json"}, "variable": [{"key": "baseurl", "value": "http://localhost:5000", "type": "string"}, {"key": "sender_email", "value": "<EMAIL>", "type": "string"}, {"key": "sender_password", "value": "your-app-password", "type": "string"}], "item": [{"name": "Health Check", "request": {"method": "GET", "header": [], "url": {"raw": "{{baseurl}}/api/v2/email/health", "host": ["{{baseurl}}"], "path": ["api", "v2", "email", "health"]}}, "response": []}, {"name": "Validate Email", "request": {"method": "POST", "header": [{"key": "Content-Type", "value": "application/json"}], "body": {"mode": "raw", "raw": "{\n    \"email\": \"<EMAIL>\",\n    \"context\": \"register\",\n    \"username\": \"sivani@123\",\n    \"first_name\": \"<PERSON><PERSON>\",\n    \"last_name\": \"<PERSON><PERSON><PERSON><PERSON>\",\n    \"tenant_id\": \"t001\"\n}"}, "url": {"raw": "{{baseurl}}/api/v2/email/validate", "host": ["{{baseurl}}"], "path": ["api", "v2", "email", "validate"]}}, "response": []}, {"name": "Send Email", "request": {"method": "POST", "header": [{"key": "Content-Type", "value": "application/json"}], "body": {"mode": "raw", "raw": "{\n    \"sender_email\": \"{{sender_email}}\",\n    \"sender_password\": \"{{sender_password}}\",\n    \"recipient_email\": \"<EMAIL>\",\n    \"subject\": \"Test Email from NSL Platform\",\n    \"message\": \"<h2>Hello from NSL Platform!</h2><p>This is a test email sent via our API.</p>\",\n    \"email_type\": \"html\"\n}"}, "url": {"raw": "{{baseurl}}/api/v2/email/send", "host": ["{{baseurl}}"], "path": ["api", "v2", "email", "send"]}}, "response": []}, {"name": "Register User with Welcome Email", "request": {"method": "POST", "header": [{"key": "Content-Type", "value": "application/json"}], "body": {"mode": "raw", "raw": "{\n    \"email\": \"<EMAIL>\",\n    \"username\": \"<PERSON><PERSON>@123\",\n    \"first_name\": \"<PERSON><PERSON>\",\n    \"last_name\": \"<PERSON><PERSON><PERSON><PERSON>\",\n    \"password\": \"sivani@123\",\n    \"tenant_id\": \"t001\",\n    \"sender_email\": \"{{sender_email}}\",\n    \"sender_password\": \"{{sender_password}}\"\n}"}, "url": {"raw": "{{baseurl}}/api/v2/email/register", "host": ["{{baseurl}}"], "path": ["api", "v2", "email", "register"]}}, "response": []}, {"name": "Login with Email Notification", "request": {"method": "POST", "header": [{"key": "Content-Type", "value": "application/json"}], "body": {"mode": "raw", "raw": "{\n    \"email\": \"<EMAIL>\",\n    \"password\": \"Sivani@123\",\n    \"send_notification\": true,\n    \"sender_email\": \"{{sender_email}}\",\n    \"sender_password\": \"{{sender_password}}\"\n}"}, "url": {"raw": "{{baseurl}}/api/v2/email/login", "host": ["{{baseurl}}"], "path": ["api", "v2", "email", "login"]}}, "response": []}, {"name": "Password Reset Email", "request": {"method": "POST", "header": [{"key": "Content-Type", "value": "application/json"}], "body": {"mode": "raw", "raw": "{\n    \"email\": \"<EMAIL>\",\n    \"password\": \"Sivani123\",\n    \"send_notification\": true,\n    \"sender_email\": \"{{sender_email}}\",\n    \"sender_password\": \"{{sender_password}}\"\n}"}, "url": {"raw": "{{baseurl}}/api/v2/email/reset-password", "host": ["{{baseurl}}"], "path": ["api", "v2", "email", "reset-password"]}}, "response": []}, {"name": "<PERSON><PERSON>", "request": {"method": "POST", "header": [{"key": "Content-Type", "value": "application/json"}], "body": {"mode": "raw", "raw": "{\n    \"email\": \"<EMAIL>\",\n    \"password\": \"Sivani123\",\n    \"send_notification\": true,\n    \"sender_email\": \"{{sender_email}}\",\n    \"sender_password\": \"{{sender_password}}\"\n}"}, "url": {"raw": "{{baseurl}}/api/v2/email/login", "host": ["{{baseurl}}"], "path": ["api", "v2", "email", "login"]}}, "response": []}]}