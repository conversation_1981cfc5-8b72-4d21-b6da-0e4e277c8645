#!/usr/bin/env python3
"""
Standalone Email Service
Works without complex dependencies - drop-in replacement
"""

import json
import smtplib
import ssl
from email.mime.text import MIMEText
from email.mime.multipart import MIMEMultipart
from email.mime.base import MIMEBase
from email import encoders
import base64
import os
import sys
from datetime import datetime
from typing import Dict, Any, Optional
import logging

# Simple HTTP server without external dependencies
from http.server import HTTPServer, BaseHTTPRequestHandler
import urllib.parse

# Configure logging
logging.basicConfig(level=logging.INFO)
logger = logging.getLogger(__name__)

class EmailServiceConfig:
    """Email service configuration"""
    def __init__(self):
        # Email configuration - UPDATE THESE VALUES
        self.company_email = "<EMAIL>"
        self.company_password = "YOUR_GMAIL_APP_PASSWORD_HERE"  # Replace with your App Password
        self.company_name = "NSL Platform"
        
        # SMTP configurations
        self.smtp_configs = {
            "gmail": {
                "server": "smtp.gmail.com",
                "port": 587,
                "use_tls": True
            },
            "outlook": {
                "server": "smtp-mail.outlook.com",
                "port": 587,
                "use_tls": True
            }
        }
        self.default_provider = "gmail"

class StandaloneEmailService:
    """Standalone email service with your exact JSON format"""
    
    def __init__(self):
        self.config = EmailServiceConfig()
        self.supported_operations = {
            "send_email": self._send_email,
            "send_bulk": self._send_bulk,
            "send_template": self._send_template,
            "send_notification": self._send_notification,
            "send_welcome": self._send_welcome,
            "send_reset": self._send_reset
        }
    
    def process_request(self, request_data: Dict[str, Any]) -> Dict[str, Any]:
        """Process email service request with your exact JSON format"""
        try:
            # Validate request structure
            if not self._validate_request(request_data):
                return self._error_response("Invalid request structure")
            
            adapter_config = request_data.get("adapter_config", {})
            service = adapter_config.get("service")
            operation = adapter_config.get("operation")
            version = adapter_config.get("version", "v1")
            
            # Validate service
            if service != "email_service":
                return self._error_response(f"Unsupported service: {service}")
            
            # Validate operation
            if operation not in self.supported_operations:
                return self._error_response(f"Unsupported operation: {operation}")
            
            # Execute operation
            parameters = request_data.get("parameters", {})
            files = request_data.get("files", {})
            
            logger.info(f"Processing {operation} request")
            result = self.supported_operations[operation](parameters, files)
            
            return {
                "status": "success",
                "service": service,
                "operation": operation,
                "version": version,
                "result": result,
                "timestamp": datetime.now().isoformat()
            }
            
        except Exception as e:
            logger.error(f"Error processing request: {str(e)}")
            return self._error_response(f"Service error: {str(e)}")
    
    def _validate_request(self, request_data: Dict[str, Any]) -> bool:
        """Validate request structure"""
        if "adapter_config" not in request_data:
            return False
        
        adapter_config = request_data.get("adapter_config", {})
        if "service" not in adapter_config or "operation" not in adapter_config:
            return False
        
        return True
    
    def _send_email(self, parameters: Dict[str, Any], files: Dict[str, Any]) -> Dict[str, Any]:
        """Send single email"""
        try:
            to_email = parameters.get("to_email")
            to_name = parameters.get("to_name", "")
            subject = parameters.get("subject", "")
            message = parameters.get("message", "")
            message_type = parameters.get("message_type", "html")
            
            if not to_email:
                return {"success": False, "error": "to_email is required"}
            
            result = self._send_smtp_email(
                to_email=to_email,
                to_name=to_name,
                subject=subject,
                message=message,
                message_type=message_type,
                attachments=files
            )
            
            return result
            
        except Exception as e:
            return {"success": False, "error": str(e)}
    
    def _send_bulk(self, parameters: Dict[str, Any], files: Dict[str, Any]) -> Dict[str, Any]:
        """Send bulk emails"""
        try:
            recipients = parameters.get("recipients", [])
            subject = parameters.get("subject", "")
            message = parameters.get("message", "")
            
            if not recipients:
                return {"success": False, "error": "recipients list is required"}
            
            results = []
            for recipient in recipients:
                if isinstance(recipient, str):
                    to_email = recipient
                    to_name = ""
                else:
                    to_email = recipient.get("email", "")
                    to_name = recipient.get("name", "")
                
                if to_email:
                    result = self._send_smtp_email(
                        to_email=to_email,
                        to_name=to_name,
                        subject=subject,
                        message=message
                    )
                    results.append({
                        "email": to_email,
                        "success": result.get("success", False)
                    })
            
            return {
                "success": True,
                "total_sent": len([r for r in results if r["success"]]),
                "results": results
            }
            
        except Exception as e:
            return {"success": False, "error": str(e)}
    
    def _send_template(self, parameters: Dict[str, Any], files: Dict[str, Any]) -> Dict[str, Any]:
        """Send template email"""
        try:
            template_name = parameters.get("template_name", "")
            to_email = parameters.get("to_email", "")
            template_data = parameters.get("template_data", {})
            
            template = self._get_template(template_name, template_data)
            if not template:
                return {"success": False, "error": f"Template '{template_name}' not found"}
            
            return self._send_smtp_email(
                to_email=to_email,
                to_name=template_data.get("user_name", ""),
                subject=template["subject"],
                message=template["message"]
            )
            
        except Exception as e:
            return {"success": False, "error": str(e)}
    
    def _send_notification(self, parameters: Dict[str, Any], files: Dict[str, Any]) -> Dict[str, Any]:
        """Send notification email"""
        try:
            to_email = parameters.get("to_email", "")
            notification_type = parameters.get("notification_type", "general")
            data = parameters.get("data", {})
            
            subject, message = self._create_notification(notification_type, data)
            
            return self._send_smtp_email(
                to_email=to_email,
                to_name=data.get("user_name", ""),
                subject=subject,
                message=message
            )
            
        except Exception as e:
            return {"success": False, "error": str(e)}
    
    def _send_welcome(self, parameters: Dict[str, Any], files: Dict[str, Any]) -> Dict[str, Any]:
        """Send welcome email"""
        try:
            to_email = parameters.get("to_email", "")
            user_name = parameters.get("user_name", "")
            user_data = parameters.get("user_data", {})
            
            template_data = {
                "user_name": user_name,
                "company_name": self.config.company_name,
                **user_data
            }
            
            return self._send_template({
                "template_name": "welcome",
                "to_email": to_email,
                "template_data": template_data
            }, files)
            
        except Exception as e:
            return {"success": False, "error": str(e)}
    
    def _send_reset(self, parameters: Dict[str, Any], files: Dict[str, Any]) -> Dict[str, Any]:
        """Send password reset email"""
        try:
            to_email = parameters.get("to_email", "")
            reset_token = parameters.get("reset_token", "")
            user_name = parameters.get("user_name", "")
            
            template_data = {
                "user_name": user_name,
                "reset_token": reset_token,
                "reset_url": f"https://your-company.com/reset?token={reset_token}",
                "company_name": self.config.company_name
            }
            
            return self._send_template({
                "template_name": "password_reset",
                "to_email": to_email,
                "template_data": template_data
            }, files)
            
        except Exception as e:
            return {"success": False, "error": str(e)}
    
    def _send_smtp_email(self, to_email: str, to_name: str = "", subject: str = "", 
                        message: str = "", message_type: str = "html", 
                        attachments: Dict = None) -> Dict[str, Any]:
        """Send email via SMTP"""
        try:
            smtp_config = self.config.smtp_configs[self.config.default_provider]
            
            # Create message
            msg = MIMEMultipart()
            msg['From'] = f"{self.config.company_name} <{self.config.company_email}>"
            msg['To'] = f"{to_name} <{to_email}>" if to_name else to_email
            msg['Subject'] = subject
            
            # Add body
            if message_type.lower() == 'html':
                msg.attach(MIMEText(message, 'html'))
            else:
                msg.attach(MIMEText(message, 'plain'))
            
            # Add attachments
            if attachments:
                for filename, file_data in attachments.items():
                    try:
                        file_content = base64.b64decode(file_data)
                        part = MIMEBase('application', 'octet-stream')
                        part.set_payload(file_content)
                        encoders.encode_base64(part)
                        part.add_header('Content-Disposition', f'attachment; filename= {filename}')
                        msg.attach(part)
                    except Exception as e:
                        logger.warning(f"Failed to add attachment {filename}: {e}")
            
            # Send email
            server = smtplib.SMTP(smtp_config['server'], smtp_config['port'])
            if smtp_config['use_tls']:
                server.starttls()
            
            server.login(self.config.company_email, self.config.company_password)
            text = msg.as_string()
            server.sendmail(self.config.company_email, to_email, text)
            server.quit()
            
            logger.info(f"Email sent successfully to {to_email}")
            
            return {
                "success": True,
                "message": "Email sent successfully",
                "to_email": to_email,
                "subject": subject,
                "timestamp": datetime.now().isoformat()
            }
            
        except Exception as e:
            logger.error(f"Failed to send email to {to_email}: {str(e)}")
            return {
                "success": False,
                "error": str(e),
                "to_email": to_email
            }
    
    def _get_template(self, template_name: str, data: Dict[str, Any]) -> Optional[Dict[str, str]]:
        """Get email template"""
        templates = {
            "welcome": {
                "subject": f"Welcome to {self.config.company_name}!",
                "message": f"""
                <html>
                <body style="font-family: Arial, sans-serif;">
                    <h2>Welcome to {self.config.company_name}, {data.get('user_name', 'User')}!</h2>
                    <p>Thank you for joining us. Your account has been successfully created.</p>
                    <p>Best regards,<br>{self.config.company_name} Team</p>
                </body>
                </html>
                """
            },
            "password_reset": {
                "subject": f"{self.config.company_name} - Password Reset",
                "message": f"""
                <html>
                <body style="font-family: Arial, sans-serif;">
                    <h2>Password Reset Request</h2>
                    <p>Hello {data.get('user_name', 'User')},</p>
                    <p>Click the link below to reset your password:</p>
                    <p><a href="{data.get('reset_url', '#')}">Reset Password</a></p>
                    <p>Best regards,<br>{self.config.company_name} Team</p>
                </body>
                </html>
                """
            }
        }
        return templates.get(template_name)
    
    def _create_notification(self, notification_type: str, data: Dict[str, Any]) -> tuple:
        """Create notification message"""
        if notification_type == "login":
            return (
                f"{self.config.company_name} - Login Notification",
                f"""
                <html>
                <body style="font-family: Arial, sans-serif;">
                    <h2>Login Notification</h2>
                    <p>Hello {data.get('user_name', 'User')},</p>
                    <p>We detected a login to your account at {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}.</p>
                    <p>Best regards,<br>{self.config.company_name} Team</p>
                </body>
                </html>
                """
            )
        else:
            return (
                f"{self.config.company_name} - Notification",
                f"""
                <html>
                <body style="font-family: Arial, sans-serif;">
                    <h2>Notification</h2>
                    <p>Hello {data.get('user_name', 'User')},</p>
                    <p>{data.get('message', 'You have a new notification.')}</p>
                    <p>Best regards,<br>{self.config.company_name} Team</p>
                </body>
                </html>
                """
            )
    
    def _error_response(self, message: str) -> Dict[str, Any]:
        """Create error response"""
        return {
            "status": "error",
            "message": message,
            "timestamp": datetime.now().isoformat()
        }

# HTTP Server for API
class EmailAPIHandler(BaseHTTPRequestHandler):
    def __init__(self, *args, **kwargs):
        self.email_service = StandaloneEmailService()
        super().__init__(*args, **kwargs)
    
    def do_GET(self):
        if self.path == '/health':
            self.send_json_response({
                "status": "success",
                "message": "Standalone Email Service is running",
                "timestamp": datetime.now().isoformat()
            })
        else:
            self.send_404()
    
    def do_POST(self):
        if self.path == '/email/process':
            content_length = int(self.headers.get('Content-Length', 0))
            if content_length > 0:
                post_data = self.rfile.read(content_length)
                try:
                    data = json.loads(post_data.decode('utf-8'))
                    result = self.email_service.process_request(data)
                    self.send_json_response(result)
                except Exception as e:
                    self.send_json_response({
                        "status": "error",
                        "message": f"Error processing request: {str(e)}"
                    })
            else:
                self.send_json_response({
                    "status": "error",
                    "message": "No data provided"
                })
        else:
            self.send_404()
    
    def send_json_response(self, data):
        self.send_response(200)
        self.send_header('Content-type', 'application/json')
        self.send_header('Access-Control-Allow-Origin', '*')
        self.end_headers()
        self.wfile.write(json.dumps(data, indent=2).encode())
    
    def send_404(self):
        self.send_response(404)
        self.end_headers()
        self.wfile.write(b'Not Found')

def run_server():
    """Run the standalone email service"""
    server_address = ('', 8080)
    httpd = HTTPServer(server_address, EmailAPIHandler)
    
    print("=" * 70)
    print("📧 STANDALONE EMAIL SERVICE")
    print("=" * 70)
    print("🌐 Server: http://localhost:8080")
    print("📋 Health: http://localhost:8080/health")
    print("📧 Endpoint: POST http://localhost:8080/email/process")
    print("=" * 70)
    print("🎯 Your JSON Format:")
    print("""{
  "adapter_config": {
    "service": "email_service",
    "operation": "send_email",
    "version": "v1"
  },
  "parameters": {
    "to_email": "<EMAIL>",
    "subject": "Test Email",
    "message": "Hello World!"
  },
  "files": {}
}""")
    print("=" * 70)
    print("🚀 Starting server...")
    
    try:
        httpd.serve_forever()
    except KeyboardInterrupt:
        print("\n👋 Server stopped!")
        httpd.shutdown()

if __name__ == "__main__":
    if len(sys.argv) > 1 and sys.argv[1] == "test":
        # Test mode
        service = StandaloneEmailService()
        test_request = {
            "adapter_config": {
                "service": "email_service",
                "operation": "send_email",
                "version": "v1"
            },
            "parameters": {
                "to_email": "<EMAIL>",
                "subject": "Test Email",
                "message": "Hello from standalone service!"
            },
            "files": {}
        }
        result = service.process_request(test_request)
        print(json.dumps(result, indent=2))
    else:
        run_server()
