#!/usr/bin/env python3
"""
Simple Email Sender - Send messages to others
Works immediately without complex setup
"""

import webbrowser
import urllib.parse
import json
import sys
from datetime import datetime

# Your Gmail Configuration
SENDER_EMAIL = "<EMAIL>"

def create_gmail_compose_url(recipient_email, subject, message):
    """Create Gmail compose URL with pre-filled content"""
    
    # Encode the email content for URL
    encoded_subject = urllib.parse.quote(subject)
    encoded_message = urllib.parse.quote(message)
    
    # Create Gmail compose URL
    gmail_compose_url = f"https://mail.google.com/mail/?view=cm&fs=1&to={recipient_email}&su={encoded_subject}&body={encoded_message}"
    
    return gmail_compose_url

def send_email_via_gmail_compose(recipient_email, recipient_name, subject, message):
    """Open Gmail compose window with pre-filled email"""
    
    try:
        print(f"📧 Preparing email for {recipient_name} ({recipient_email})")
        print(f"📋 Subject: {subject}")
        print(f"⏰ Time: {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}")
        
        # Create full message with signature
        full_message = f"""Hello {recipient_name},

{message}

Best regards,
Sivani Dornadula
NSL Platform
Email: {SENDER_EMAIL}
Sent: {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}

---
This email was composed using NSL Platform Email System"""
        
        # Create Gmail compose URL
        compose_url = create_gmail_compose_url(recipient_email, subject, full_message)
        
        print("🌐 Opening Gmail compose window...")
        webbrowser.open(compose_url)
        
        print("✅ Gmail compose opened successfully!")
        print("📝 Email is pre-filled and ready to send")
        print("👆 Just click 'Send' in Gmail to send the email")
        
        return {
            "status": "success",
            "message": "Gmail compose opened with pre-filled email",
            "sender": SENDER_EMAIL,
            "recipient": recipient_email,
            "subject": subject,
            "timestamp": datetime.now().isoformat(),
            "compose_url": compose_url,
            "instructions": "Click 'Send' in Gmail to send the email"
        }
        
    except Exception as e:
        print(f"❌ Error: {str(e)}")
        return {
            "status": "error",
            "message": f"Failed to open Gmail compose: {str(e)}",
            "error_details": str(e)
        }

def send_quick_message():
    """Quick message sending function"""
    print("=" * 70)
    print("📧 SEND EMAIL TO OTHERS - SIVANI'S GMAIL")
    print("=" * 70)
    print(f"📤 From: {SENDER_EMAIL}")
    print("🎯 Method: Gmail Compose (Pre-filled)")
    print("=" * 70)
    
    # Get recipient details
    print("👤 Enter recipient details:")
    recipient_email = input("📧 Recipient Email: ").strip()
    
    if not recipient_email:
        print("❌ Recipient email is required!")
        return
    
    recipient_name = input("👤 Recipient Name (optional): ").strip()
    if not recipient_name:
        recipient_name = recipient_email.split('@')[0].replace('.', ' ').title()
    
    # Get email content
    print(f"\n📝 Compose email for {recipient_name}:")
    subject = input("📋 Subject: ").strip()
    if not subject:
        subject = f"Message from Sivani - {datetime.now().strftime('%Y-%m-%d')}"
    
    print("💬 Message:")
    message = input().strip()
    if not message:
        message = "Hello! I hope you're doing well. This is a message from NSL Platform."
    
    # Send email
    print("\n🚀 Opening Gmail compose...")
    result = send_email_via_gmail_compose(recipient_email, recipient_name, subject, message)
    
    # Show result
    print("\n" + "=" * 70)
    if result["status"] == "success":
        print("🎉 GMAIL COMPOSE OPENED!")
        print(f"✅ To: {recipient_email}")
        print(f"✅ Subject: {subject}")
        print("✅ Email is pre-filled and ready to send")
        print("👆 Just click 'Send' in Gmail!")
    else:
        print("❌ FAILED TO OPEN GMAIL COMPOSE!")
        print(f"❌ Error: {result['message']}")
    
    print("=" * 70)

def send_predefined_messages():
    """Send predefined message templates"""
    print("=" * 70)
    print("📧 PREDEFINED MESSAGE TEMPLATES")
    print("=" * 70)
    
    templates = {
        "1": {
            "name": "Friendly Hello",
            "subject": "Hello from Sivani!",
            "message": "Hi! I hope you're doing well. I wanted to reach out and say hello. How have you been?"
        },
        "2": {
            "name": "Meeting Request",
            "subject": "Meeting Request - NSL Platform",
            "message": "Hello! I would like to schedule a meeting with you to discuss NSL Platform. Please let me know your availability this week."
        },
        "3": {
            "name": "Project Update",
            "subject": "NSL Platform - Project Update",
            "message": "Hello! I wanted to share an exciting update about our NSL Platform project. The email system is now fully functional and working great!"
        },
        "4": {
            "name": "Thank You",
            "subject": "Thank You!",
            "message": "Thank you so much for your time and support. I really appreciate everything you've done to help with the NSL Platform project."
        }
    }
    
    print("📋 Available templates:")
    for key, template in templates.items():
        print(f"   {key}. {template['name']}")
    
    choice = input("\n👉 Select template (1-4): ").strip()
    
    if choice not in templates:
        print("❌ Invalid choice!")
        return
    
    template = templates[choice]
    
    # Get recipient
    recipient_email = input("📧 Recipient Email: ").strip()
    if not recipient_email:
        print("❌ Recipient email is required!")
        return
    
    recipient_name = input("👤 Recipient Name (optional): ").strip()
    if not recipient_name:
        recipient_name = recipient_email.split('@')[0].replace('.', ' ').title()
    
    # Send email
    print(f"\n🚀 Opening Gmail compose for '{template['name']}'...")
    result = send_email_via_gmail_compose(
        recipient_email, 
        recipient_name, 
        template['subject'], 
        template['message']
    )
    
    # Show result
    if result["status"] == "success":
        print("🎉 GMAIL COMPOSE OPENED!")
        print(f"✅ Template: {template['name']}")
        print(f"✅ To: {recipient_email}")
        print("👆 Just click 'Send' in Gmail!")
    else:
        print("❌ FAILED TO OPEN GMAIL COMPOSE!")
        print(f"❌ Error: {result['message']}")

def main():
    """Main function"""
    print("=" * 70)
    print("📧 SIMPLE EMAIL SENDER - SIVANI'S GMAIL")
    print("=" * 70)
    print(f"📤 Sender: {SENDER_EMAIL}")
    print("🎯 Method: Gmail Compose (No SMTP needed)")
    print("✅ Advantage: Works immediately!")
    print("=" * 70)
    
    while True:
        print("\n🔧 Choose an option:")
        print("1. Send custom message")
        print("2. Send predefined template")
        print("3. Open Gmail directly")
        print("4. Exit")
        
        try:
            choice = input("\n👉 Enter your choice (1-4): ").strip()
            
            if choice == "1":
                send_quick_message()
                
            elif choice == "2":
                send_predefined_messages()
                
            elif choice == "3":
                print("\n🌐 Opening Gmail...")
                webbrowser.open("https://mail.google.com/mail/u/0/#inbox")
                print("✅ Gmail opened!")
                
            elif choice == "4":
                print("\n👋 Goodbye!")
                break
                
            else:
                print("\n❌ Invalid choice. Please enter 1, 2, 3, or 4.")
                
        except KeyboardInterrupt:
            print("\n\n👋 Goodbye!")
            break
        except Exception as e:
            print(f"\n❌ Error: {str(e)}")

def send_email_json(recipient_email, recipient_name="", subject="", message=""):
    """Send email and return JSON response"""
    try:
        if not recipient_email:
            return {
                "status": "error",
                "message": "Recipient email is required",
                "success": False
            }

        if not recipient_name:
            recipient_name = recipient_email.split('@')[0].replace('.', ' ').title()

        if not subject:
            subject = f"Message from Sivani - {datetime.now().strftime('%Y-%m-%d')}"

        if not message:
            message = "Hello! This is a message from NSL Platform."

        # Send email
        result = send_email_via_gmail_compose(recipient_email, recipient_name, subject, message)

        # Add success flag for easier checking
        result["success"] = result["status"] == "success"

        return result

    except Exception as e:
        return {
            "status": "error",
            "message": f"Error sending email: {str(e)}",
            "success": False,
            "error_details": str(e)
        }

def quick_send_to_friend(friend_email, friend_name="Friend", message="Hi! Hope you're doing well!"):
    """Quick function to send email to a friend"""
    subject = f"Hello from Sivani!"
    return send_email_json(friend_email, friend_name, subject, message)

def send_business_email(recipient_email, recipient_name="", subject="", message=""):
    """Send professional business email"""
    if not subject:
        subject = "Message from Sivani - NSL Platform"

    if not message:
        message = "Hello! I hope this message finds you well. I wanted to reach out regarding NSL Platform."

    return send_email_json(recipient_email, recipient_name, subject, message)

def test_email_system():
    """Test the email system with sample data"""
    print("🧪 Testing Email System...")

    # Test data
    test_emails = [
        {
            "email": "<EMAIL>",
            "name": "Test User",
            "subject": "Test Email from Sivani",
            "message": "This is a test email from NSL Platform!"
        },
        {
            "email": "<EMAIL>",
            "name": "Best Friend",
            "subject": "Hello Friend!",
            "message": "Hi! Just wanted to say hello and see how you're doing."
        }
    ]

    for i, test_data in enumerate(test_emails, 1):
        print(f"\n📧 Test {i}: Sending to {test_data['name']}")
        result = send_email_json(
            test_data['email'],
            test_data['name'],
            test_data['subject'],
            test_data['message']
        )

        print(f"✅ Result: {result['status']}")
        if result['success']:
            print(f"📧 Gmail opened for: {test_data['email']}")
        else:
            print(f"❌ Error: {result['message']}")

if __name__ == "__main__":
    # Check if running with command line arguments
    if len(sys.argv) > 1:
        if sys.argv[1] == "test":
            test_email_system()
        elif sys.argv[1] == "json":
            # JSON mode for API integration
            print("📧 JSON Email Sender Ready")
            print("Usage examples:")
            print("result = send_email_json('<EMAIL>', 'Friend', 'Hello', 'Hi there!')")
            print("result = quick_send_to_friend('<EMAIL>')")
            print("result = send_business_email('<EMAIL>')")
        else:
            print("Available commands: test, json")
    else:
        main()
