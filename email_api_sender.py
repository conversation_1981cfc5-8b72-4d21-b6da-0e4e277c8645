#!/usr/bin/env python3
"""
Email API Sender - Send emails to others via API
Flask API for sending <NAME_EMAIL> to any recipient
"""

import smtplib
import ssl
from email.mime.text import MIMEText
from email.mime.multipart import MI<PERSON><PERSON><PERSON><PERSON><PERSON>
from datetime import datetime
import webbrowser
import json

# Simple HTTP server without Flask to avoid conflicts
from http.server import HTTPServer, BaseHTTPRequestHandler
import urllib.parse
import threading

# Your Gmail Configuration
SENDER_EMAIL = "<EMAIL>"
SENDER_PASSWORD = "YOUR_GMAIL_APP_PASSWORD_HERE"  # Replace with your Gmail App Password

class EmailSender:
    def __init__(self):
        self.sender_email = SENDER_EMAIL
        self.sender_password = SENDER_PASSWORD
        self.smtp_server = "smtp.gmail.com"
        self.smtp_port = 587
    
    def send_email(self, recipient_email, subject, message, message_type="html"):
        """Send email to another person"""
        try:
            # Create message
            msg = MIMEMultipart()
            msg['From'] = self.sender_email
            msg['To'] = recipient_email
            msg['Subject'] = subject
            
            # Add body to email
            if message_type.lower() == 'html':
                msg.attach(MIMEText(message, 'html'))
            else:
                msg.attach(MIMEText(message, 'plain'))
            
            # Create SMTP session
            server = smtplib.SMTP(self.smtp_server, self.smtp_port)
            server.starttls()  # Enable security
            server.login(self.sender_email, self.sender_password)
            
            # Send email
            text = msg.as_string()
            server.sendmail(self.sender_email, recipient_email, text)
            server.quit()
            
            return {
                "status": "success",
                "message": "Email sent successfully",
                "sender": self.sender_email,
                "recipient": recipient_email,
                "subject": subject,
                "timestamp": datetime.now().isoformat(),
                "sent": True
            }
            
        except Exception as e:
            return {
                "status": "error",
                "message": f"Failed to send email: {str(e)}",
                "error_details": str(e),
                "sent": False
            }
    
    def create_professional_email(self, recipient_name, message_content):
        """Create a professional email template"""
        html_message = f"""
        <html>
        <body style="font-family: Arial, sans-serif; max-width: 600px; margin: 0 auto;">
            <div style="background-color: #007bff; color: white; padding: 20px; text-align: center;">
                <h1>NSL Platform</h1>
                <h2>Message from Sivani Dornadula</h2>
            </div>
            
            <div style="padding: 20px; background-color: #f8f9fa;">
                <h3>Hello {recipient_name},</h3>
                
                <div style="background-color: white; padding: 20px; border-radius: 5px; margin: 20px 0; border-left: 4px solid #007bff;">
                    {message_content}
                </div>
                
                <div style="background-color: #e9ecef; padding: 15px; border-radius: 5px; margin-top: 20px;">
                    <p><strong>📧 From:</strong> {self.sender_email}</p>
                    <p><strong>⏰ Sent:</strong> {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}</p>
                    <p><strong>🏢 Platform:</strong> NSL Platform</p>
                </div>
                
                <p style="margin-top: 30px;">Best regards,<br>
                <strong>Sivani Dornadula</strong><br>
                NSL Platform Team<br>
                Email: {self.sender_email}</p>
            </div>
            
            <div style="background-color: #6c757d; color: white; padding: 10px; text-align: center; font-size: 12px;">
                <p>This email was sent from NSL Platform Email System</p>
            </div>
        </body>
        </html>
        """
        return html_message

# Initialize email sender
email_sender = EmailSender()

class EmailAPIHandler(BaseHTTPRequestHandler):
    def do_GET(self):
        """Handle GET requests"""
        if self.path == '/health':
            self.send_response(200)
            self.send_header('Content-type', 'application/json')
            self.send_header('Access-Control-Allow-Origin', '*')
            self.end_headers()
            
            response = {
                "status": "success",
                "message": "Email API Sender is running",
                "sender_email": SENDER_EMAIL,
                "timestamp": datetime.now().isoformat(),
                "endpoints": [
                    "GET /health - Health check",
                    "POST /send-email - Send email to others",
                    "POST /send-quick - Send quick email",
                    "POST /open-gmail - Open Gmail"
                ]
            }
            
            self.wfile.write(json.dumps(response, indent=2).encode())
        else:
            self.send_response(404)
            self.end_headers()
            self.wfile.write(b'Not Found')
    
    def do_POST(self):
        """Handle POST requests"""
        content_length = int(self.headers['Content-Length'])
        post_data = self.rfile.read(content_length)
        
        try:
            data = json.loads(post_data.decode('utf-8'))
        except:
            data = {}
        
        if self.path == '/send-email':
            self.handle_send_email(data)
        elif self.path == '/send-quick':
            self.handle_send_quick(data)
        elif self.path == '/open-gmail':
            self.handle_open_gmail(data)
        else:
            self.send_response(404)
            self.end_headers()
            self.wfile.write(b'Not Found')
    
    def handle_send_email(self, data):
        """Handle email sending"""
        try:
            recipient_email = data.get('recipient_email', '').strip()
            recipient_name = data.get('recipient_name', '').strip()
            subject = data.get('subject', 'Message from Sivani - NSL Platform')
            message = data.get('message', 'Hello! This is a message from NSL Platform.')
            
            if not recipient_email:
                self.send_error_response("Recipient email is required")
                return
            
            if not recipient_name:
                recipient_name = recipient_email.split('@')[0]
            
            # Create professional email
            html_content = email_sender.create_professional_email(recipient_name, message)
            
            # Send email
            result = email_sender.send_email(recipient_email, subject, html_content, "html")
            
            self.send_json_response(result)
            
        except Exception as e:
            self.send_error_response(f"Error sending email: {str(e)}")
    
    def handle_send_quick(self, data):
        """Handle quick email sending with predefined templates"""
        try:
            recipient_email = data.get('recipient_email', '').strip()
            template = data.get('template', 'test')
            
            if not recipient_email:
                self.send_error_response("Recipient email is required")
                return
            
            templates = {
                'test': {
                    'subject': 'Test Email from NSL Platform',
                    'message': 'Hello! This is a test email from Sivani\'s NSL Platform system. The email system is working perfectly!'
                },
                'meeting': {
                    'subject': 'Meeting Invitation - NSL Platform',
                    'message': 'Hello! I would like to invite you to a meeting to discuss NSL Platform. Please let me know your availability.'
                },
                'update': {
                    'subject': 'NSL Platform - Project Update',
                    'message': 'Hello! I wanted to share an update on the NSL Platform project. The email system has been successfully implemented!'
                }
            }
            
            if template not in templates:
                template = 'test'
            
            template_data = templates[template]
            recipient_name = recipient_email.split('@')[0]
            
            # Create professional email
            html_content = email_sender.create_professional_email(recipient_name, template_data['message'])
            
            # Send email
            result = email_sender.send_email(recipient_email, template_data['subject'], html_content, "html")
            result['template_used'] = template
            
            self.send_json_response(result)
            
        except Exception as e:
            self.send_error_response(f"Error sending quick email: {str(e)}")
    
    def handle_open_gmail(self, data):
        """Handle Gmail opening"""
        try:
            gmail_url = "https://mail.google.com/mail/u/0/#sent"
            webbrowser.open(gmail_url)
            
            response = {
                "status": "success",
                "message": "Gmail opened successfully",
                "gmail_url": gmail_url,
                "sender_email": SENDER_EMAIL,
                "timestamp": datetime.now().isoformat()
            }
            
            self.send_json_response(response)
            
        except Exception as e:
            self.send_error_response(f"Error opening Gmail: {str(e)}")
    
    def send_json_response(self, data):
        """Send JSON response"""
        self.send_response(200)
        self.send_header('Content-type', 'application/json')
        self.send_header('Access-Control-Allow-Origin', '*')
        self.end_headers()
        self.wfile.write(json.dumps(data, indent=2).encode())
    
    def send_error_response(self, message):
        """Send error response"""
        self.send_response(400)
        self.send_header('Content-type', 'application/json')
        self.send_header('Access-Control-Allow-Origin', '*')
        self.end_headers()
        
        error_response = {
            "status": "error",
            "message": message,
            "timestamp": datetime.now().isoformat()
        }
        
        self.wfile.write(json.dumps(error_response, indent=2).encode())

def run_server():
    """Run the email API server"""
    server_address = ('', 8080)
    httpd = HTTPServer(server_address, EmailAPIHandler)
    
    print("=" * 70)
    print("📧 EMAIL API SENDER - SIVANI'S GMAIL")
    print("=" * 70)
    print(f"📤 Sender: {SENDER_EMAIL}")
    print("🌐 Server: http://localhost:8080")
    print("=" * 70)
    print("📋 Available endpoints:")
    print("   GET  /health")
    print("   POST /send-email")
    print("   POST /send-quick")
    print("   POST /open-gmail")
    print("=" * 70)
    print("🎯 Example usage:")
    print("   POST /send-email")
    print("   {")
    print('     "recipient_email": "<EMAIL>",')
    print('     "recipient_name": "Friend Name",')
    print('     "subject": "Hello from Sivani",')
    print('     "message": "Hi! How are you?"')
    print("   }")
    print("=" * 70)
    
    if SENDER_PASSWORD == "YOUR_GMAIL_APP_PASSWORD_HERE":
        print("⚠️  WARNING: Gmail App Password not configured!")
        print("📝 Update SENDER_PASSWORD in this file")
        print("=" * 70)
    
    print("🚀 Starting server...")
    print("=" * 70)
    
    try:
        httpd.serve_forever()
    except KeyboardInterrupt:
        print("\n👋 Server stopped!")
        httpd.shutdown()

if __name__ == "__main__":
    run_server()
