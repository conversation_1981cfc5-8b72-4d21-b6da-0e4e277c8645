#!/usr/bin/env python3
"""
Quick Gmail Test - Works without App Password for testing
This version opens Gmail directly and simulates email sending
"""

import webbrowser
import time
from flask import Flask, request, jsonify
from flask_cors import CORS
import json
from datetime import datetime
import uuid

app = Flask(__name__)
CORS(app)

# Your Gmail Configuration
GMAIL_EMAIL = "<EMAIL>"

class QuickGmailSystem:
    def __init__(self):
        self.gmail_email = GMAIL_EMAIL
    
    def simulate_email_and_open_gmail(self, recipient_email, subject, message):
        """
        Simulate email sending and open Gmail directly
        This works without SMTP for immediate testing
        """
        try:
            # Simulate email sending (no actual SMTP)
            print(f"📧 Simulating email send:")
            print(f"   From: {self.gmail_email}")
            print(f"   To: {recipient_email}")
            print(f"   Subject: {subject}")
            print(f"   Time: {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}")
            
            # Open Gmail directly
            gmail_url = "https://mail.google.com/mail/u/0/#inbox"
            webbrowser.open(gmail_url)
            
            return {
                "status": "success",
                "message": "Gmail opened successfully (email simulated)",
                "sender": self.gmail_email,
                "recipient": recipient_email,
                "subject": subject,
                "timestamp": datetime.now().isoformat(),
                "gmail_opened": True,
                "gmail_url": gmail_url,
                "note": "Email sending simulated - Gmail opened directly"
            }
            
        except Exception as e:
            return {
                "status": "error",
                "message": f"Failed to open Gmail: {str(e)}",
                "error_details": str(e),
                "gmail_opened": False
            }
    
    def create_login_notification_content(self, user_email, user_name="User"):
        """Create login notification email content"""
        subject = f"Login Notification - NSL Platform ({user_name})"
        
        message = f"""
        Login Notification for {user_name}
        
        Email: {user_email}
        Time: {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}
        Platform: NSL Platform
        Status: ✅ Successful Login
        
        Gmail will open automatically to check your emails.
        """
        
        return subject, message

# Initialize system
gmail_system = QuickGmailSystem()

@app.route('/api/v2/email/health', methods=['GET'])
def health_check():
    """Health check endpoint"""
    return jsonify({
        "status": "success",
        "message": "Quick Gmail System is running",
        "gmail_email": GMAIL_EMAIL,
        "timestamp": datetime.now().isoformat(),
        "version": "Quick Test 1.0",
        "note": "This version opens Gmail directly without SMTP"
    })

@app.route('/api/v2/email/login', methods=['POST'])
def quick_gmail_login():
    """
    Quick login that opens Gmail directly
    POST /api/v2/email/login
    Body: {
        "email": "<EMAIL>",
        "password": "any_password"
    }
    """
    try:
        data = request.get_json()
        
        if not data:
            return jsonify({
                "status": "error",
                "message": "No JSON data provided"
            }), 400
        
        user_email = data.get('email', '').strip()
        user_password = data.get('password', '')
        
        if not user_email:
            return jsonify({
                "status": "error",
                "message": "Email is required"
            }), 400
        
        # Extract user name from email
        user_name = user_email.split('@')[0].replace('.', ' ').title()
        
        # Create login notification content
        subject, message = gmail_system.create_login_notification_content(user_email, user_name)
        
        # Simulate email and open Gmail
        email_result = gmail_system.simulate_email_and_open_gmail(
            recipient_email=user_email,
            subject=subject,
            message=message
        )
        
        # Create response
        response = {
            "status": "success",
            "message": "Login successful - Gmail opened!",
            "user_email": user_email,
            "user_name": user_name,
            "login_token": str(uuid.uuid4()),
            "expires_in": 3600,
            "email_result": email_result,
            "gmail_opened": email_result.get("gmail_opened", False),
            "instructions": [
                "1. Gmail has opened in your browser",
                "2. Check your inbox for notifications",
                "3. Email sending was simulated (no SMTP needed)",
                "4. For real email sending, set up Gmail App Password"
            ]
        }
        
        return jsonify(response)
        
    except Exception as e:
        return jsonify({
            "status": "error",
            "message": f"Login failed: {str(e)}",
            "error_details": str(e)
        }), 500

@app.route('/api/v2/email/open-gmail', methods=['POST'])
def open_gmail_directly():
    """
    Open Gmail directly in browser
    POST /api/v2/email/open-gmail
    """
    try:
        gmail_url = "https://mail.google.com/mail/u/0/#inbox"
        webbrowser.open(gmail_url)
        
        return jsonify({
            "status": "success",
            "message": "Gmail opened successfully!",
            "gmail_url": gmail_url,
            "gmail_email": GMAIL_EMAIL,
            "timestamp": datetime.now().isoformat(),
            "note": "Gmail opened in your default browser"
        })
        
    except Exception as e:
        return jsonify({
            "status": "error",
            "message": f"Failed to open Gmail: {str(e)}",
            "error_details": str(e)
        }), 500

@app.route('/api/v2/email/sivani-login', methods=['POST'])
def sivani_direct_login():
    """
    Direct login for Sivani's email
    POST /api/v2/email/sivani-login
    """
    try:
        # Use Sivani's email directly
        user_email = GMAIL_EMAIL
        user_name = "Sivani Dornadula"
        
        # Create login notification content
        subject, message = gmail_system.create_login_notification_content(user_email, user_name)
        
        # Simulate email and open Gmail
        email_result = gmail_system.simulate_email_and_open_gmail(
            recipient_email=user_email,
            subject=subject,
            message=message
        )
        
        # Create response
        response = {
            "status": "success",
            "message": f"Welcome {user_name}! Gmail opened successfully!",
            "user_email": user_email,
            "user_name": user_name,
            "login_token": str(uuid.uuid4()),
            "expires_in": 3600,
            "email_result": email_result,
            "gmail_opened": email_result.get("gmail_opened", False),
            "welcome_message": f"Hello {user_name}! Your Gmail is now open.",
            "instructions": [
                "✅ Gmail opened automatically",
                "✅ Login simulation completed",
                "✅ Check your Gmail inbox",
                "✅ System is working perfectly!"
            ]
        }
        
        return jsonify(response)
        
    except Exception as e:
        return jsonify({
            "status": "error",
            "message": f"Login failed: {str(e)}",
            "error_details": str(e)
        }), 500

if __name__ == '__main__':
    print("=" * 70)
    print("🚀 QUICK GMAIL TEST SYSTEM")
    print("=" * 70)
    print(f"📧 Gmail Account: {GMAIL_EMAIL}")
    print("🌐 Available endpoints:")
    print("   GET  /api/v2/email/health")
    print("   POST /api/v2/email/login")
    print("   POST /api/v2/email/open-gmail")
    print("   POST /api/v2/email/sivani-login")
    print("=" * 70)
    print("🎯 QUICK TEST:")
    print("   Hit any endpoint → Gmail opens immediately!")
    print("   No SMTP setup needed for testing")
    print("=" * 70)
    print("✅ ADVANTAGES:")
    print("   ✓ Works immediately")
    print("   ✓ No App Password needed")
    print("   ✓ Gmail opens directly")
    print("   ✓ Perfect for testing")
    print("=" * 70)
    print("🚀 Starting server on http://localhost:5000")
    print("=" * 70)
    
    app.run(debug=True, host='0.0.0.0', port=5000)
