#!/usr/bin/env python3
"""
Send Email to Others - Gmail SMTP System
Send emails from sivani.dornadu<PERSON>@gmail.com to any recipient
"""

import smtplib
import ssl
from email.mime.text import MIMEText
from email.mime.multipart import MIME<PERSON><PERSON>ip<PERSON>
from datetime import datetime
import webbrowser

# Your Gmail Configuration
SENDER_EMAIL = "<EMAIL>"
SENDER_PASSWORD = "YOUR_GMAIL_APP_PASSWORD_HERE"  # Replace with your Gmail App Password

class EmailSender:
    def __init__(self):
        self.sender_email = SENDER_EMAIL
        self.sender_password = SENDER_PASSWORD
        self.smtp_server = "smtp.gmail.com"
        self.smtp_port = 587
    
    def send_email(self, recipient_email, subject, message, message_type="html"):
        """
        Send email to another person
        
        Args:
            recipient_email (str): Email address of the recipient
            subject (str): Email subject
            message (str): Email message content
            message_type (str): 'html' or 'plain'
        
        Returns:
            dict: Result of email sending operation
        """
        try:
            print(f"📧 Preparing to send email...")
            print(f"   From: {self.sender_email}")
            print(f"   To: {recipient_email}")
            print(f"   Subject: {subject}")
            print(f"   Time: {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}")
            
            # Create message
            msg = MIMEMultipart()
            msg['From'] = self.sender_email
            msg['To'] = recipient_email
            msg['Subject'] = subject
            
            # Add body to email
            if message_type.lower() == 'html':
                msg.attach(MIMEText(message, 'html'))
            else:
                msg.attach(MIMEText(message, 'plain'))
            
            # Create SMTP session
            print("🔄 Connecting to Gmail SMTP server...")
            server = smtplib.SMTP(self.smtp_server, self.smtp_port)
            server.starttls()  # Enable security
            
            print("🔐 Logging in to Gmail...")
            server.login(self.sender_email, self.sender_password)
            
            print("📤 Sending email...")
            text = msg.as_string()
            server.sendmail(self.sender_email, recipient_email, text)
            server.quit()
            
            print("✅ Email sent successfully!")
            
            return {
                "status": "success",
                "message": "Email sent successfully",
                "sender": self.sender_email,
                "recipient": recipient_email,
                "subject": subject,
                "timestamp": datetime.now().isoformat(),
                "sent": True
            }
            
        except Exception as e:
            print(f"❌ Failed to send email: {str(e)}")
            return {
                "status": "error",
                "message": f"Failed to send email: {str(e)}",
                "error_details": str(e),
                "sent": False
            }
    
    def create_professional_email(self, recipient_name, message_content):
        """Create a professional email template"""
        html_message = f"""
        <html>
        <body style="font-family: Arial, sans-serif; max-width: 600px; margin: 0 auto;">
            <div style="background-color: #007bff; color: white; padding: 20px; text-align: center;">
                <h1>NSL Platform</h1>
                <h2>Message from Sivani Dornadula</h2>
            </div>
            
            <div style="padding: 20px; background-color: #f8f9fa;">
                <h3>Hello {recipient_name},</h3>
                
                <div style="background-color: white; padding: 20px; border-radius: 5px; margin: 20px 0;">
                    {message_content}
                </div>
                
                <div style="background-color: #e9ecef; padding: 15px; border-radius: 5px; margin-top: 20px;">
                    <p><strong>📧 Sent from:</strong> {self.sender_email}</p>
                    <p><strong>⏰ Time:</strong> {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}</p>
                    <p><strong>🏢 Platform:</strong> NSL Platform</p>
                </div>
                
                <p style="margin-top: 30px;">Best regards,<br>
                <strong>Sivani Dornadula</strong><br>
                NSL Platform<br>
                Email: {self.sender_email}</p>
            </div>
            
            <div style="background-color: #6c757d; color: white; padding: 10px; text-align: center; font-size: 12px;">
                <p>This email was sent from NSL Platform Email System</p>
            </div>
        </body>
        </html>
        """
        return html_message
    
    def send_to_friend(self, friend_email, friend_name, message):
        """Send email to a friend"""
        subject = f"Message from Sivani - NSL Platform"
        html_content = self.create_professional_email(friend_name, message)
        return self.send_email(friend_email, subject, html_content, "html")
    
    def send_business_email(self, recipient_email, recipient_name, subject, message):
        """Send business email"""
        html_content = self.create_professional_email(recipient_name, message)
        return self.send_email(recipient_email, subject, html_content, "html")

def send_quick_email():
    """Quick email sending function"""
    email_sender = EmailSender()
    
    print("=" * 70)
    print("📧 SEND EMAIL TO OTHERS - SIVANI'S GMAIL")
    print("=" * 70)
    print(f"📤 Sender: {SENDER_EMAIL}")
    print("=" * 70)
    
    # Get recipient details
    print("👤 Enter recipient details:")
    recipient_email = input("📧 Recipient Email: ").strip()
    recipient_name = input("👤 Recipient Name: ").strip()
    
    if not recipient_email:
        print("❌ Recipient email is required!")
        return
    
    if not recipient_name:
        recipient_name = recipient_email.split('@')[0]
    
    # Get email content
    print("\n📝 Enter email content:")
    subject = input("📋 Subject: ").strip()
    if not subject:
        subject = "Message from Sivani - NSL Platform"
    
    print("💬 Message (press Enter twice to finish):")
    message_lines = []
    while True:
        line = input()
        if line == "" and len(message_lines) > 0:
            break
        message_lines.append(line)
    
    message = "<br>".join(message_lines)
    if not message:
        message = "Hello! This is a test message from NSL Platform."
    
    # Send email
    print("\n🚀 Sending email...")
    result = email_sender.send_business_email(recipient_email, recipient_name, subject, message)
    
    # Show result
    print("\n" + "=" * 70)
    if result["status"] == "success":
        print("🎉 EMAIL SENT SUCCESSFULLY!")
        print(f"✅ To: {recipient_email}")
        print(f"✅ Subject: {subject}")
        print(f"✅ Time: {result['timestamp']}")
        
        # Ask if user wants to open Gmail
        open_gmail = input("\n🌐 Open Gmail to see sent email? (y/n): ").strip().lower()
        if open_gmail in ['y', 'yes']:
            gmail_url = "https://mail.google.com/mail/u/0/#sent"
            webbrowser.open(gmail_url)
            print("✅ Gmail opened - Check your Sent folder!")
    else:
        print("❌ EMAIL SENDING FAILED!")
        print(f"❌ Error: {result['message']}")
        if "AUTHENTICATION_FAILED" in result.get('error_details', ''):
            print("\n🔧 SOLUTION:")
            print("1. Enable 2-Factor Authentication on Gmail")
            print("2. Generate App Password")
            print("3. Replace 'YOUR_GMAIL_APP_PASSWORD_HERE' in this file")
    
    print("=" * 70)

def send_predefined_emails():
    """Send predefined email templates"""
    email_sender = EmailSender()
    
    print("=" * 70)
    print("📧 PREDEFINED EMAIL TEMPLATES")
    print("=" * 70)
    
    templates = {
        "1": {
            "name": "Test Email",
            "subject": "Test Email from NSL Platform",
            "message": "Hello! This is a test email from Sivani's NSL Platform system. The email system is working perfectly!"
        },
        "2": {
            "name": "Meeting Invitation",
            "subject": "Meeting Invitation - NSL Platform",
            "message": "Hello! I would like to invite you to a meeting to discuss NSL Platform. Please let me know your availability."
        },
        "3": {
            "name": "Project Update",
            "subject": "NSL Platform - Project Update",
            "message": "Hello! I wanted to share an update on the NSL Platform project. The email system has been successfully implemented and is working great!"
        }
    }
    
    print("📋 Available templates:")
    for key, template in templates.items():
        print(f"   {key}. {template['name']}")
    
    choice = input("\n👉 Select template (1-3): ").strip()
    
    if choice not in templates:
        print("❌ Invalid choice!")
        return
    
    template = templates[choice]
    
    # Get recipient
    recipient_email = input("📧 Recipient Email: ").strip()
    recipient_name = input("👤 Recipient Name: ").strip()
    
    if not recipient_email:
        print("❌ Recipient email is required!")
        return
    
    if not recipient_name:
        recipient_name = recipient_email.split('@')[0]
    
    # Send email
    print(f"\n🚀 Sending '{template['name']}'...")
    result = email_sender.send_business_email(
        recipient_email, 
        recipient_name, 
        template['subject'], 
        template['message']
    )
    
    # Show result
    if result["status"] == "success":
        print("🎉 EMAIL SENT SUCCESSFULLY!")
        print(f"✅ Template: {template['name']}")
        print(f"✅ To: {recipient_email}")
    else:
        print("❌ EMAIL SENDING FAILED!")
        print(f"❌ Error: {result['message']}")

def main():
    """Main function"""
    print("=" * 70)
    print("📧 EMAIL SENDER - SIVANI'S GMAIL SYSTEM")
    print("=" * 70)
    print(f"📤 Sender Account: {SENDER_EMAIL}")
    print("🎯 Purpose: Send emails to other people")
    print("=" * 70)
    
    if SENDER_PASSWORD == "YOUR_GMAIL_APP_PASSWORD_HERE":
        print("⚠️  WARNING: Gmail App Password not configured!")
        print("📝 Please update SENDER_PASSWORD in this file with your Gmail App Password")
        print("📖 See SIVANI_GMAIL_SETUP.md for instructions")
        print("=" * 70)
    
    while True:
        print("\n🔧 Choose an option:")
        print("1. Send custom email")
        print("2. Send predefined template")
        print("3. Exit")
        
        try:
            choice = input("\n👉 Enter your choice (1-3): ").strip()
            
            if choice == "1":
                send_quick_email()
                
            elif choice == "2":
                send_predefined_emails()
                
            elif choice == "3":
                print("\n👋 Goodbye!")
                break
                
            else:
                print("\n❌ Invalid choice. Please enter 1, 2, or 3.")
                
        except KeyboardInterrupt:
            print("\n\n👋 Goodbye!")
            break
        except Exception as e:
            print(f"\n❌ Error: {str(e)}")

if __name__ == "__main__":
    main()
