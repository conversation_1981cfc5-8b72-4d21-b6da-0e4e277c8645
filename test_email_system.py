#!/usr/bin/env python3
"""
Test script for NSL Email System
Run this to verify everything is working correctly
"""

import requests
import json
import time

# Configuration
API_BASE_URL = "http://localhost:5000"
TEST_EMAIL = "<EMAIL>"

# You need to update these with your actual Gmail credentials
SENDER_EMAIL = "<EMAIL>"  # Replace with your Gmail
SENDER_PASSWORD = "your-app-password"  # Replace with your Gmail App Password

def test_health_check():
    """Test the health check endpoint"""
    print("🔍 Testing Health Check...")
    try:
        response = requests.get(f"{API_BASE_URL}/api/v2/email/health")
        if response.status_code == 200:
            print("✅ Health Check: PASSED")
            print(f"   Response: {response.json()}")
            return True
        else:
            print("❌ Health Check: FAILED")
            print(f"   Status Code: {response.status_code}")
            return False
    except Exception as e:
        print(f"❌ Health Check: ERROR - {str(e)}")
        return False

def test_email_validation():
    """Test email validation endpoint"""
    print("\n🔍 Testing Email Validation...")
    
    test_data = {
        "email": "<EMAIL>",
        "context": "register",
        "username": "testuser12",
        "first_name": "Durga",
        "last_name": "Prasad",
        "tenant_id": "t001"
    }
    
    try:
        response = requests.post(
            f"{API_BASE_URL}/api/v2/email/validate",
            json=test_data,
            headers={"Content-Type": "application/json"}
        )
        
        if response.status_code == 200:
            print("✅ Email Validation: PASSED")
            result = response.json()
            print(f"   Email: {result.get('email')}")
            print(f"   Valid: {result.get('is_valid')}")
            print(f"   Context: {result.get('context')}")
            return True
        else:
            print("❌ Email Validation: FAILED")
            print(f"   Status Code: {response.status_code}")
            print(f"   Response: {response.text}")
            return False
    except Exception as e:
        print(f"❌ Email Validation: ERROR - {str(e)}")
        return False

def test_send_email():
    """Test sending email (requires valid Gmail credentials)"""
    print("\n🔍 Testing Email Sending...")
    
    if SENDER_EMAIL == "<EMAIL>" or SENDER_PASSWORD == "your-app-password":
        print("⚠️  Email Sending: SKIPPED")
        print("   Please update SENDER_EMAIL and SENDER_PASSWORD in this script")
        return True
    
    test_data = {
        "sender_email": SENDER_EMAIL,
        "sender_password": SENDER_PASSWORD,
        "recipient_email": TEST_EMAIL,
        "subject": "NSL Platform Test Email",
        "message": "<h2>Test Email</h2><p>This is a test email from NSL Platform API!</p>",
        "email_type": "html"
    }
    
    try:
        response = requests.post(
            f"{API_BASE_URL}/api/v2/email/send",
            json=test_data,
            headers={"Content-Type": "application/json"}
        )
        
        if response.status_code == 200:
            print("✅ Email Sending: PASSED")
            result = response.json()
            print(f"   Status: {result.get('status')}")
            print(f"   Message: {result.get('message')}")
            return True
        else:
            print("❌ Email Sending: FAILED")
            print(f"   Status Code: {response.status_code}")
            print(f"   Response: {response.text}")
            return False
    except Exception as e:
        print(f"❌ Email Sending: ERROR - {str(e)}")
        return False

def test_user_registration():
    """Test user registration with welcome email"""
    print("\n🔍 Testing User Registration...")
    
    test_data = {
        "email": "<EMAIL>",
        "username": "testuser12",
        "first_name": "Durga",
        "last_name": "Prasad",
        "password": "test@123",
        "tenant_id": "t001"
    }
    
    # Add sender credentials if available
    if SENDER_EMAIL != "<EMAIL>" and SENDER_PASSWORD != "your-app-password":
        test_data["sender_email"] = SENDER_EMAIL
        test_data["sender_password"] = SENDER_PASSWORD
    
    try:
        response = requests.post(
            f"{API_BASE_URL}/api/v2/email/register",
            json=test_data,
            headers={"Content-Type": "application/json"}
        )
        
        if response.status_code == 200:
            print("✅ User Registration: PASSED")
            result = response.json()
            print(f"   Status: {result.get('status')}")
            print(f"   Email: {result.get('email')}")
            print(f"   Context: {result.get('context')}")
            
            if 'email_sent' in result:
                email_status = result['email_sent'].get('status', 'unknown')
                print(f"   Welcome Email: {email_status}")
            
            return True
        else:
            print("❌ User Registration: FAILED")
            print(f"   Status Code: {response.status_code}")
            print(f"   Response: {response.text}")
            return False
    except Exception as e:
        print(f"❌ User Registration: ERROR - {str(e)}")
        return False

def test_user_login():
    """Test user login with notification email"""
    print("\n🔍 Testing User Login...")
    
    test_data = {
        "email": "<EMAIL>",
        "password": "test@123",
        "send_notification": True
    }
    
    # Add sender credentials if available
    if SENDER_EMAIL != "<EMAIL>" and SENDER_PASSWORD != "your-app-password":
        test_data["sender_email"] = SENDER_EMAIL
        test_data["sender_password"] = SENDER_PASSWORD
    
    try:
        response = requests.post(
            f"{API_BASE_URL}/api/v2/email/login",
            json=test_data,
            headers={"Content-Type": "application/json"}
        )
        
        if response.status_code == 200:
            print("✅ User Login: PASSED")
            result = response.json()
            print(f"   Status: {result.get('status')}")
            print(f"   Email: {result.get('email')}")
            print(f"   Token: {result.get('token', 'N/A')[:20]}...")
            
            if 'email_sent' in result:
                email_status = result['email_sent'].get('status', 'unknown')
                print(f"   Login Notification: {email_status}")
            
            return True
        else:
            print("❌ User Login: FAILED")
            print(f"   Status Code: {response.status_code}")
            print(f"   Response: {response.text}")
            return False
    except Exception as e:
        print(f"❌ User Login: ERROR - {str(e)}")
        return False

def main():
    """Run all tests"""
    print("=" * 60)
    print("NSL PLATFORM EMAIL SYSTEM - TEST SUITE")
    print("=" * 60)
    
    # Check if API server is running
    print("🚀 Checking if API server is running...")
    try:
        response = requests.get(f"{API_BASE_URL}/api/v2/email/health", timeout=5)
        print("✅ API Server is running!")
    except Exception as e:
        print("❌ API Server is not running!")
        print("   Please start the server with: python email_api.py")
        return
    
    # Run tests
    tests = [
        test_health_check,
        test_email_validation,
        test_send_email,
        test_user_registration,
        test_user_login
    ]
    
    passed = 0
    total = len(tests)
    
    for test in tests:
        if test():
            passed += 1
        time.sleep(1)  # Small delay between tests
    
    # Summary
    print("\n" + "=" * 60)
    print("TEST SUMMARY")
    print("=" * 60)
    print(f"Tests Passed: {passed}/{total}")
    
    if passed == total:
        print("🎉 ALL TESTS PASSED! Email system is working correctly.")
    else:
        print("⚠️  Some tests failed. Check the output above for details.")
    
    print("\n📝 NEXT STEPS:")
    print("1. Update Gmail credentials in this script for full email testing")
    print("2. Import the Postman collection for manual testing")
    print("3. Test with real email addresses")
    print("4. Check Gmail for received emails")
    
    print("\n🔗 POSTMAN COLLECTION:")
    print("   File: NSL_Email_API.postman_collection.json")
    print("   Update variables: sender_email, sender_password")

if __name__ == "__main__":
    main()
