import smtplib
from email.mime.text import MIMEText
import requests

def send_email_smtp(to_email, subject, body, smtp_server, smtp_port, smtp_user, smtp_pass):
    msg = MIMEText(body)
    msg['Subject'] = subject
    msg['From'] = smtp_user
    msg['To'] = to_email

    with smtplib.SMTP_SSL(smtp_server, smtp_port) as server:
        server.login(smtp_user, smtp_pass)
        server.sendmail(smtp_user, [to_email], msg.as_string())

def login_and_notify(email, password):
    # 1. Login API call (replace URL and payload as needed)
    login_url = "http://10.26.1.52:8000/api/v2/auth/login"
    payload = {"username": email, "password": password}
    resp = requests.post(login_url, json=payload)
    if resp.status_code == 200:
        print("Login successful!")
        # 2. Send notification email
        send_email_smtp(
            to_email=email,
            subject="Login Notification",
            body="You have successfully logged in to NSL Platform.",
            smtp_server="smtp.gmail.com",
            smtp_port=465,
            smtp_user="<EMAIL>",      # <-- your SMTP email
            smtp_pass="your_app_password"          # <-- your app password (not your Gmail password)
        )
        # 3. Example: API hit after login (e.g., fetch profile)
        token = resp.json().get("access_token")
        if token:
            headers = {"Authorization": f"Bearer {token}"}
            profile_url = f"http://10.26.1.52:8000/api/v2/auth/profile/U14"
            profile_resp = requests.get(profile_url, headers=headers)
            print("Profile API response:", profile_resp.json())
    else:
        print("Login failed:", resp.text)

# Usage example:
if __name__ == "__main__":
    # Try with username if email fails
    login_and_notify("sivani.dornadula", "Sivani@123")