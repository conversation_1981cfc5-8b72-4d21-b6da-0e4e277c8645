#!/usr/bin/env python3
"""
Working Email Sender - Send emails to others
Uses Gmail SMTP to send real <NAME_EMAIL>
"""

import sys
import os
from datetime import datetime
import webbrowser
import urllib.parse

# Add current directory to Python path to avoid conflicts
sys.path.insert(0, os.path.dirname(os.path.abspath(__file__)))

# Import SMTP modules with error handling
try:
    import smtplib
    import ssl
    from email.mime.text import MIMEText
    from email.mime.multipart import MIMEMultipart
    SMTP_AVAILABLE = True
    print("✅ SMTP modules loaded successfully")
except ImportError as e:
    SMTP_AVAILABLE = False
    print(f"⚠️  SMTP not available: {e}")
    print("📝 Will use Gmail Compose method instead")

# Your Gmail Configuration
SENDER_EMAIL = "<EMAIL>"
SENDER_PASSWORD = "YOUR_GMAIL_APP_PASSWORD_HERE"  # Replace with your Gmail App Password

class WorkingEmailSender:
    def __init__(self):
        self.sender_email = SENDER_EMAIL
        self.sender_password = SENDER_PASSWORD
        self.smtp_server = "smtp.gmail.com"
        self.smtp_port = 587
        self.smtp_available = SMTP_AVAILABLE
    
    def send_email_smtp(self, recipient_email, subject, message):
        """Send email using SMTP (requires App Password)"""
        if not self.smtp_available:
            return {
                "status": "error",
                "message": "SMTP not available - use Gmail Compose method instead",
                "sent": False
            }
        
        try:
            print(f"📧 Sending email via SMTP...")
            print(f"   From: {self.sender_email}")
            print(f"   To: {recipient_email}")
            print(f"   Subject: {subject}")
            
            # Create message
            msg = MIMEMultipart()
            msg['From'] = self.sender_email
            msg['To'] = recipient_email
            msg['Subject'] = subject
            
            # Add HTML body
            html_body = f"""
            <html>
            <body style="font-family: Arial, sans-serif;">
                <h2 style="color: #007bff;">Message from Sivani Dornadula</h2>
                <div style="padding: 20px; background-color: #f8f9fa; border-radius: 5px;">
                    {message}
                </div>
                <hr>
                <p><strong>From:</strong> {self.sender_email}</p>
                <p><strong>Sent:</strong> {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}</p>
                <p><strong>Platform:</strong> NSL Platform</p>
            </body>
            </html>
            """
            
            msg.attach(MIMEText(html_body, 'html'))
            
            # Send email
            server = smtplib.SMTP(self.smtp_server, self.smtp_port)
            server.starttls()
            server.login(self.sender_email, self.sender_password)
            
            text = msg.as_string()
            server.sendmail(self.sender_email, recipient_email, text)
            server.quit()
            
            print("✅ Email sent successfully via SMTP!")
            
            return {
                "status": "success",
                "message": "Email sent successfully via SMTP",
                "method": "SMTP",
                "sender": self.sender_email,
                "recipient": recipient_email,
                "subject": subject,
                "timestamp": datetime.now().isoformat(),
                "sent": True
            }
            
        except Exception as e:
            print(f"❌ SMTP failed: {str(e)}")
            return {
                "status": "error",
                "message": f"SMTP failed: {str(e)}",
                "error_details": str(e),
                "sent": False
            }
    
    def send_email_compose(self, recipient_email, subject, message):
        """Send email using Gmail Compose (always works)"""
        try:
            print(f"📧 Opening Gmail Compose...")
            print(f"   From: {self.sender_email}")
            print(f"   To: {recipient_email}")
            print(f"   Subject: {subject}")
            
            # Create full message with signature
            full_message = f"""{message}

---
Best regards,
Sivani Dornadula
NSL Platform
Email: {self.sender_email}
Sent: {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}

This email was composed using NSL Platform Email System"""
            
            # Encode for URL
            encoded_subject = urllib.parse.quote(subject)
            encoded_message = urllib.parse.quote(full_message)
            
            # Create Gmail compose URL
            compose_url = f"https://mail.google.com/mail/?view=cm&fs=1&to={recipient_email}&su={encoded_subject}&body={encoded_message}"
            
            # Open Gmail
            webbrowser.open(compose_url)
            
            print("✅ Gmail Compose opened successfully!")
            print("👆 Click 'Send' in Gmail to send the email")
            
            return {
                "status": "success",
                "message": "Gmail Compose opened with pre-filled email",
                "method": "Gmail Compose",
                "sender": self.sender_email,
                "recipient": recipient_email,
                "subject": subject,
                "timestamp": datetime.now().isoformat(),
                "compose_url": compose_url,
                "instructions": "Click 'Send' in Gmail to send the email"
            }
            
        except Exception as e:
            print(f"❌ Gmail Compose failed: {str(e)}")
            return {
                "status": "error",
                "message": f"Gmail Compose failed: {str(e)}",
                "error_details": str(e)
            }
    
    def send_email(self, recipient_email, subject, message, method="auto"):
        """Send email using best available method"""
        if method == "smtp" or (method == "auto" and self.smtp_available and self.sender_password != "YOUR_GMAIL_APP_PASSWORD_HERE"):
            return self.send_email_smtp(recipient_email, subject, message)
        else:
            return self.send_email_compose(recipient_email, subject, message)

def send_email_to_person():
    """Interactive email sending"""
    email_sender = WorkingEmailSender()
    
    print("=" * 70)
    print("📧 SEND EMAIL TO OTHERS - SIVANI'S GMAIL")
    print("=" * 70)
    print(f"📤 From: {SENDER_EMAIL}")
    
    if email_sender.smtp_available and SENDER_PASSWORD != "YOUR_GMAIL_APP_PASSWORD_HERE":
        print("🔧 Method: SMTP (Direct sending)")
    else:
        print("🔧 Method: Gmail Compose (Pre-filled)")
        if not email_sender.smtp_available:
            print("⚠️  SMTP not available - using Gmail Compose")
        else:
            print("⚠️  App Password needed for SMTP - using Gmail Compose")
    
    print("=" * 70)
    
    # Get recipient details
    print("👤 Enter recipient details:")
    recipient_email = input("📧 Recipient Email: ").strip()
    
    if not recipient_email:
        print("❌ Recipient email is required!")
        return
    
    recipient_name = input("👤 Recipient Name (optional): ").strip()
    if not recipient_name:
        recipient_name = recipient_email.split('@')[0].replace('.', ' ').title()
    
    # Get email content
    print(f"\n📝 Compose email for {recipient_name}:")
    subject = input("📋 Subject: ").strip()
    if not subject:
        subject = f"Message from Sivani - {datetime.now().strftime('%Y-%m-%d')}"
    
    print("💬 Message:")
    message = input().strip()
    if not message:
        message = f"Hello {recipient_name}! I hope you're doing well. This is a message from NSL Platform."
    
    # Send email
    print("\n🚀 Sending email...")
    result = email_sender.send_email(recipient_email, subject, message)
    
    # Show result
    print("\n" + "=" * 70)
    if result["status"] == "success":
        print("🎉 EMAIL PROCESS COMPLETED!")
        print(f"✅ Method: {result.get('method', 'Unknown')}")
        print(f"✅ To: {recipient_email}")
        print(f"✅ Subject: {subject}")
        
        if result.get('method') == 'SMTP':
            print("✅ Email sent directly via SMTP!")
        else:
            print("✅ Gmail Compose opened - Click 'Send' to send!")
            
        # Ask if user wants to open Gmail
        if result.get('method') == 'SMTP':
            open_gmail = input("\n🌐 Open Gmail to see sent email? (y/n): ").strip().lower()
            if open_gmail in ['y', 'yes']:
                webbrowser.open("https://mail.google.com/mail/u/0/#sent")
                print("✅ Gmail opened - Check your Sent folder!")
    else:
        print("❌ EMAIL PROCESS FAILED!")
        print(f"❌ Error: {result['message']}")
        
        if "AUTHENTICATION_FAILED" in result.get('error_details', ''):
            print("\n🔧 SOLUTION:")
            print("1. Enable 2-Factor Authentication on Gmail")
            print("2. Generate App Password")
            print("3. Replace 'YOUR_GMAIL_APP_PASSWORD_HERE' in this file")
    
    print("=" * 70)

def send_quick_templates():
    """Send predefined email templates"""
    email_sender = WorkingEmailSender()
    
    templates = {
        "1": {
            "name": "Friendly Hello",
            "subject": "Hello from Sivani!",
            "message": "Hi! I hope you're doing well. I wanted to reach out and say hello. How have you been lately?"
        },
        "2": {
            "name": "Meeting Request",
            "subject": "Meeting Request - NSL Platform",
            "message": "Hello! I would like to schedule a meeting with you to discuss NSL Platform. Please let me know your availability this week."
        },
        "3": {
            "name": "Project Update",
            "subject": "NSL Platform - Exciting Update!",
            "message": "Hello! I wanted to share an exciting update about our NSL Platform project. The email system is now fully functional and working perfectly!"
        },
        "4": {
            "name": "Thank You Note",
            "subject": "Thank You!",
            "message": "Thank you so much for your time and support. I really appreciate everything you've done to help with our projects."
        }
    }
    
    print("=" * 70)
    print("📧 QUICK EMAIL TEMPLATES")
    print("=" * 70)
    
    print("📋 Available templates:")
    for key, template in templates.items():
        print(f"   {key}. {template['name']}")
    
    choice = input("\n👉 Select template (1-4): ").strip()
    
    if choice not in templates:
        print("❌ Invalid choice!")
        return
    
    template = templates[choice]
    
    # Get recipient
    recipient_email = input("📧 Recipient Email: ").strip()
    if not recipient_email:
        print("❌ Recipient email is required!")
        return
    
    # Send email
    print(f"\n🚀 Sending '{template['name']}'...")
    result = email_sender.send_email(recipient_email, template['subject'], template['message'])
    
    # Show result
    if result["status"] == "success":
        print("🎉 EMAIL PROCESS COMPLETED!")
        print(f"✅ Template: {template['name']}")
        print(f"✅ To: {recipient_email}")
        
        if result.get('method') == 'SMTP':
            print("✅ Email sent directly!")
        else:
            print("✅ Gmail Compose opened - Click 'Send'!")
    else:
        print("❌ EMAIL PROCESS FAILED!")
        print(f"❌ Error: {result['message']}")

def main():
    """Main function"""
    print("=" * 70)
    print("📧 WORKING EMAIL SENDER - SIVANI'S GMAIL")
    print("=" * 70)
    print(f"📤 Sender: {SENDER_EMAIL}")
    print(f"🔧 SMTP Available: {'✅ Yes' if SMTP_AVAILABLE else '❌ No'}")
    
    if SENDER_PASSWORD == "YOUR_GMAIL_APP_PASSWORD_HERE":
        print("⚠️  App Password: Not configured (will use Gmail Compose)")
    else:
        print("✅ App Password: Configured (can use SMTP)")
    
    print("=" * 70)
    
    while True:
        print("\n🔧 Choose an option:")
        print("1. Send custom email")
        print("2. Send template email")
        print("3. Open Gmail")
        print("4. Exit")
        
        try:
            choice = input("\n👉 Enter your choice (1-4): ").strip()
            
            if choice == "1":
                send_email_to_person()
                
            elif choice == "2":
                send_quick_templates()
                
            elif choice == "3":
                print("\n🌐 Opening Gmail...")
                webbrowser.open("https://mail.google.com/mail/u/0/#inbox")
                print("✅ Gmail opened!")
                
            elif choice == "4":
                print("\n👋 Goodbye!")
                break
                
            else:
                print("\n❌ Invalid choice. Please enter 1, 2, 3, or 4.")
                
        except KeyboardInterrupt:
            print("\n\n👋 Goodbye!")
            break
        except Exception as e:
            print(f"\n❌ Error: {str(e)}")

if __name__ == "__main__":
    main()
