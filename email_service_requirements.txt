# Email Service Dependencies
# Core framework
fastapi==0.104.1
uvicorn[standard]==0.24.0
pydantic==2.5.0
pydantic-settings==2.1.0

# Async file handling
aiofiles==23.2.0

# Email and SMTP
smtplib2==0.2.1
email-validator==2.1.0

# File handling and multipart
python-multipart==0.0.6

# Template engine
jinja2==3.1.2

# Environment variables
python-dotenv==1.0.0

# HTTP client (if needed)
httpx==0.25.2
requests==2.31.0

# Logging and monitoring
structlog==23.2.0

# Security
cryptography==41.0.8

# Database (if needed)
sqlalchemy==2.0.23
alembic==1.13.1

# Testing
pytest==7.4.3
pytest-asyncio==0.21.1
httpx==0.25.2

# Development
black==23.11.0
flake8==6.1.0
mypy==1.7.1

# Production server
gunicorn==21.2.0

# Additional utilities
python-dateutil==2.8.2
pytz==2023.3
