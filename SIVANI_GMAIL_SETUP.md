# 🚀 Gmail Direct System Setup for Sivani

## 📧 **Your Gmail Account**
- **Email**: `<EMAIL>`
- **Current Password**: `<PERSON><PERSON>@123`

## ⚠️ **IMPORTANT: Gmail App Password Required**

Gmail no longer accepts regular passwords for SMTP. You need to create an **App Password**.

### **Step 1: Enable 2-Factor Authentication**

1. Go to [Google Account Settings](https://myaccount.google.com/)
2. Click **"Security"** in the left menu
3. Under **"Signing in to Google"**, click **"2-Step Verification"**
4. Follow the setup process (use your phone number)

### **Step 2: Generate App Password**

1. Go back to [Google Account Settings](https://myaccount.google.com/)
2. Click **"Security"** → **"App passwords"**
3. Select **"Mail"** and **"Windows Computer"**
4. Click **"Generate"**
5. **Copy the 16-character password** (example: `abcd efgh ijkl mnop`)

### **Step 3: Update the Code**

Replace `<PERSON>vani@123` with your App Password in the code:

```python
GMAIL_CONFIG = {
    "email": "<EMAIL>",
    "password": "abcd efgh ijkl mnop",  # Your 16-character App Password
    "smtp_server": "smtp.gmail.com",
    "smtp_port": 587
}
```

## 🚀 **How to Run the System**

### **1. Start the Server**
```bash
python gmail_direct_system.py
```

### **2. Import Postman Collection**
- File: `Gmail_Direct_System.postman_collection.json`
- Import into Postman

### **3. Test the API**

#### **Health Check**
```
GET http://localhost:5000/api/v2/email/health
```

#### **Login and Open Gmail**
```json
POST http://localhost:5000/api/v2/email/login
{
    "email": "<EMAIL>",
    "password": "Sivani@123",
    "open_gmail": true
}
```

## 🎯 **What Happens When You Hit the API**

1. **API receives your request**
2. **Sends login notification email** to the specified email
3. **Gmail opens automatically** in your browser
4. **You can see the email** in your inbox

## 📊 **API Response Example**

```json
{
    "status": "success",
    "message": "Login successful - Email sent and Gmail opened",
    "user_email": "<EMAIL>",
    "user_name": "Sivani Dornadula",
    "login_token": "uuid-token-here",
    "expires_in": 3600,
    "email_result": {
        "status": "success",
        "message": "Email sent successfully",
        "sender": "<EMAIL>",
        "recipient": "<EMAIL>",
        "gmail_opened": true,
        "gmail_url": "https://mail.google.com/mail/u/0/#inbox"
    },
    "gmail_opened": true
}
```

## 🔧 **Available Endpoints**

| Endpoint | Method | Description |
|----------|--------|-------------|
| `/api/v2/email/health` | GET | Check system status |
| `/api/v2/email/login` | POST | Login + send email + open Gmail |
| `/api/v2/email/send-and-open` | POST | Send custom email + open Gmail |
| `/api/v2/email/open-gmail` | POST | Just open Gmail |

## 🎯 **Quick Test Steps**

1. **Get Gmail App Password** (follow steps above)
2. **Update the code** with your App Password
3. **Run**: `python gmail_direct_system.py`
4. **Open Postman** and import collection
5. **Hit the "Login Sivani Email" request**
6. **Gmail opens automatically!**

## 🚨 **Troubleshooting**

### **"Authentication failed"**
- ✅ Make sure 2FA is enabled
- ✅ Use App Password, not regular password
- ✅ Remove spaces from App Password

### **"Gmail doesn't open"**
- ✅ Check if you have a default browser set
- ✅ Try the "Open Gmail Directly" endpoint

### **"Email not received"**
- ✅ Check spam folder
- ✅ Verify App Password is correct
- ✅ Check Gmail SMTP settings

## 📝 **Email Template**

The system sends a beautiful HTML email with:
- ✅ Login notification
- ✅ User details
- ✅ Timestamp
- ✅ Professional styling
- ✅ Direct Gmail link

## 🎉 **Success Flow**

1. **Hit API** → `POST /api/v2/email/login`
2. **Email sent** → Login notification to your Gmail
3. **Gmail opens** → Browser opens Gmail automatically
4. **Check inbox** → See the notification email

Your system is ready! Just get the App Password and test it! 🚀
