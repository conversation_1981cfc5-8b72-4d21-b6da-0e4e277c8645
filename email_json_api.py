#!/usr/bin/env python3
"""
Email JSON API - Simple HTTP server for email sending
Works with JSON requests and responses
"""

import json
import webbrowser
import urllib.parse
from datetime import datetime
from http.server import HTTPServer, BaseHTTPRequestHandler
import threading

# Your Gmail Configuration
SENDER_EMAIL = "<EMAIL>"

class EmailJSONAPI:
    def __init__(self):
        self.sender_email = SENDER_EMAIL
    
    def create_gmail_compose_url(self, recipient_email, subject, message):
        """Create Gmail compose URL with pre-filled content"""
        # Encode the email content for URL
        encoded_subject = urllib.parse.quote(subject)
        encoded_message = urllib.parse.quote(message)
        
        # Create Gmail compose URL
        gmail_compose_url = f"https://mail.google.com/mail/?view=cm&fs=1&to={recipient_email}&su={encoded_subject}&body={encoded_message}"
        
        return gmail_compose_url
    
    def send_email(self, recipient_email, recipient_name="", subject="", message=""):
        """Send email via Gmail Compose and return JSON response"""
        try:
            if not recipient_email:
                return {
                    "status": "error",
                    "message": "Recipient email is required",
                    "success": False,
                    "timestamp": datetime.now().isoformat()
                }
            
            if not recipient_name:
                recipient_name = recipient_email.split('@')[0].replace('.', ' ').title()
            
            if not subject:
                subject = f"Message from Sivani - {datetime.now().strftime('%Y-%m-%d')}"
            
            if not message:
                message = "Hello! This is a message from NSL Platform."
            
            # Create full message with signature
            full_message = f"""Hello {recipient_name},

{message}

Best regards,
Sivani Dornadula
NSL Platform
Email: {self.sender_email}
Sent: {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}

---
This email was composed using NSL Platform Email System"""
            
            # Create Gmail compose URL
            compose_url = self.create_gmail_compose_url(recipient_email, subject, full_message)
            
            # Open Gmail
            webbrowser.open(compose_url)
            
            return {
                "status": "success",
                "message": "Gmail compose opened with pre-filled email",
                "success": True,
                "data": {
                    "sender": self.sender_email,
                    "recipient": recipient_email,
                    "recipient_name": recipient_name,
                    "subject": subject,
                    "compose_url": compose_url,
                    "instructions": "Click 'Send' in Gmail to send the email"
                },
                "timestamp": datetime.now().isoformat()
            }
            
        except Exception as e:
            return {
                "status": "error",
                "message": f"Failed to open Gmail compose: {str(e)}",
                "success": False,
                "error_details": str(e),
                "timestamp": datetime.now().isoformat()
            }
    
    def send_template_email(self, recipient_email, template_name="friendly"):
        """Send predefined template email"""
        templates = {
            "friendly": {
                "subject": "Hello from Sivani!",
                "message": "Hi! I hope you're doing well. I wanted to reach out and say hello. How have you been?"
            },
            "meeting": {
                "subject": "Meeting Request - NSL Platform",
                "message": "Hello! I would like to schedule a meeting with you to discuss NSL Platform. Please let me know your availability this week."
            },
            "update": {
                "subject": "NSL Platform - Project Update",
                "message": "Hello! I wanted to share an exciting update about our NSL Platform project. The email system is now fully functional and working great!"
            },
            "thanks": {
                "subject": "Thank You!",
                "message": "Thank you so much for your time and support. I really appreciate everything you've done to help with the NSL Platform project."
            }
        }
        
        if template_name not in templates:
            template_name = "friendly"
        
        template = templates[template_name]
        recipient_name = recipient_email.split('@')[0].replace('.', ' ').title()
        
        result = self.send_email(recipient_email, recipient_name, template['subject'], template['message'])
        result['template_used'] = template_name
        
        return result

# Initialize email API
email_api = EmailJSONAPI()

class EmailAPIHandler(BaseHTTPRequestHandler):
    def do_GET(self):
        """Handle GET requests"""
        if self.path == '/':
            self.send_html_response()
        elif self.path == '/health':
            self.send_health_response()
        elif self.path == '/open-gmail':
            self.open_gmail()
        else:
            self.send_404()
    
    def do_POST(self):
        """Handle POST requests"""
        content_length = int(self.headers.get('Content-Length', 0))
        if content_length > 0:
            post_data = self.rfile.read(content_length)
            try:
                data = json.loads(post_data.decode('utf-8'))
            except:
                data = {}
        else:
            data = {}
        
        if self.path == '/send-email':
            self.handle_send_email(data)
        elif self.path == '/send-template':
            self.handle_send_template(data)
        elif self.path == '/quick-send':
            self.handle_quick_send(data)
        else:
            self.send_404()
    
    def handle_send_email(self, data):
        """Handle custom email sending"""
        recipient_email = data.get('recipient_email', '').strip()
        recipient_name = data.get('recipient_name', '').strip()
        subject = data.get('subject', '').strip()
        message = data.get('message', '').strip()
        
        result = email_api.send_email(recipient_email, recipient_name, subject, message)
        self.send_json_response(result)
    
    def handle_send_template(self, data):
        """Handle template email sending"""
        recipient_email = data.get('recipient_email', '').strip()
        template_name = data.get('template', 'friendly').strip()
        
        result = email_api.send_template_email(recipient_email, template_name)
        self.send_json_response(result)
    
    def handle_quick_send(self, data):
        """Handle quick email sending"""
        recipient_email = data.get('recipient_email', '').strip()
        message = data.get('message', 'Hi! Hope you are doing well!').strip()
        
        subject = f"Hello from Sivani!"
        recipient_name = recipient_email.split('@')[0].replace('.', ' ').title()
        
        result = email_api.send_email(recipient_email, recipient_name, subject, message)
        self.send_json_response(result)
    
    def send_health_response(self):
        """Send health check response"""
        response = {
            "status": "success",
            "message": "Email JSON API is running",
            "sender_email": SENDER_EMAIL,
            "timestamp": datetime.now().isoformat(),
            "endpoints": {
                "GET /": "API documentation",
                "GET /health": "Health check",
                "GET /open-gmail": "Open Gmail directly",
                "POST /send-email": "Send custom email",
                "POST /send-template": "Send template email",
                "POST /quick-send": "Send quick email"
            }
        }
        self.send_json_response(response)
    
    def open_gmail(self):
        """Open Gmail directly"""
        try:
            webbrowser.open("https://mail.google.com/mail/u/0/#inbox")
            response = {
                "status": "success",
                "message": "Gmail opened successfully",
                "gmail_url": "https://mail.google.com/mail/u/0/#inbox",
                "timestamp": datetime.now().isoformat()
            }
        except Exception as e:
            response = {
                "status": "error",
                "message": f"Failed to open Gmail: {str(e)}",
                "timestamp": datetime.now().isoformat()
            }
        
        self.send_json_response(response)
    
    def send_html_response(self):
        """Send HTML documentation"""
        html = f"""
        <!DOCTYPE html>
        <html>
        <head>
            <title>Email JSON API - Sivani's Gmail</title>
            <style>
                body {{ font-family: Arial, sans-serif; max-width: 800px; margin: 0 auto; padding: 20px; }}
                .endpoint {{ background: #f5f5f5; padding: 15px; margin: 10px 0; border-radius: 5px; }}
                .method {{ color: #007bff; font-weight: bold; }}
                pre {{ background: #e9ecef; padding: 10px; border-radius: 3px; }}
            </style>
        </head>
        <body>
            <h1>📧 Email JSON API - Sivani's Gmail</h1>
            <p><strong>Sender:</strong> {SENDER_EMAIL}</p>
            <p><strong>Status:</strong> ✅ Running</p>
            
            <h2>📋 Available Endpoints</h2>
            
            <div class="endpoint">
                <h3><span class="method">GET</span> /health</h3>
                <p>Health check and API information</p>
            </div>
            
            <div class="endpoint">
                <h3><span class="method">POST</span> /send-email</h3>
                <p>Send custom email</p>
                <pre>{{
    "recipient_email": "<EMAIL>",
    "recipient_name": "Friend Name",
    "subject": "Hello from Sivani",
    "message": "Hi! How are you?"
}}</pre>
            </div>
            
            <div class="endpoint">
                <h3><span class="method">POST</span> /send-template</h3>
                <p>Send template email</p>
                <pre>{{
    "recipient_email": "<EMAIL>",
    "template": "friendly"
}}</pre>
                <p>Templates: friendly, meeting, update, thanks</p>
            </div>
            
            <div class="endpoint">
                <h3><span class="method">POST</span> /quick-send</h3>
                <p>Send quick email</p>
                <pre>{{
    "recipient_email": "<EMAIL>",
    "message": "Hi! Hope you're doing well!"
}}</pre>
            </div>
            
            <div class="endpoint">
                <h3><span class="method">GET</span> /open-gmail</h3>
                <p>Open Gmail directly in browser</p>
            </div>
            
            <h2>🎯 How it Works</h2>
            <ol>
                <li>Send POST request with email details</li>
                <li>API opens Gmail Compose with pre-filled email</li>
                <li>Click 'Send' in Gmail to send the email</li>
                <li>Get JSON response with status</li>
            </ol>
            
            <p><strong>Time:</strong> {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}</p>
        </body>
        </html>
        """
        
        self.send_response(200)
        self.send_header('Content-type', 'text/html')
        self.send_header('Access-Control-Allow-Origin', '*')
        self.end_headers()
        self.wfile.write(html.encode())
    
    def send_json_response(self, data):
        """Send JSON response"""
        self.send_response(200)
        self.send_header('Content-type', 'application/json')
        self.send_header('Access-Control-Allow-Origin', '*')
        self.end_headers()
        self.wfile.write(json.dumps(data, indent=2).encode())
    
    def send_404(self):
        """Send 404 response"""
        self.send_response(404)
        self.send_header('Content-type', 'application/json')
        self.end_headers()
        error_response = {
            "status": "error",
            "message": "Endpoint not found",
            "timestamp": datetime.now().isoformat()
        }
        self.wfile.write(json.dumps(error_response).encode())

def run_server():
    """Run the email JSON API server"""
    server_address = ('', 9000)
    httpd = HTTPServer(server_address, EmailAPIHandler)
    
    print("=" * 70)
    print("📧 EMAIL JSON API - SIVANI'S GMAIL")
    print("=" * 70)
    print(f"📤 Sender: {SENDER_EMAIL}")
    print("🌐 Server: http://localhost:9000")
    print("📋 Documentation: http://localhost:9000")
    print("=" * 70)
    print("🎯 Quick Test:")
    print("   curl -X POST http://localhost:9000/quick-send \\")
    print('   -H "Content-Type: application/json" \\')
    print('   -d \'{"recipient_email":"<EMAIL>"}\'')
    print("=" * 70)
    print("🚀 Starting server...")
    print("=" * 70)
    
    try:
        httpd.serve_forever()
    except KeyboardInterrupt:
        print("\n👋 Server stopped!")
        httpd.shutdown()

if __name__ == "__main__":
    run_server()
