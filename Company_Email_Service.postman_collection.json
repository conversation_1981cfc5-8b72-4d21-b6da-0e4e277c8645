{"info": {"_postman_id": "company-email-service-001", "name": "Company Email Service API", "description": "Professional email service with adapter pattern for enterprise use", "schema": "https://schema.getpostman.com/json/collection/v2.1.0/collection.json"}, "variable": [{"key": "baseurl", "value": "http://localhost:7000", "type": "string"}, {"key": "company_email", "value": "<EMAIL>", "type": "string"}], "item": [{"name": "Health Check", "request": {"method": "GET", "header": [], "url": {"raw": "{{baseurl}}/api/v1/email/health", "host": ["{{baseurl}}"], "path": ["api", "v1", "email", "health"]}}, "response": []}, {"name": "Send Email (Adapter Pattern)", "request": {"method": "POST", "header": [{"key": "Content-Type", "value": "application/json"}], "body": {"mode": "raw", "raw": "{\n  \"adapter_config\": {\n    \"service\": \"email_service\",\n    \"operation\": \"send_email\",\n    \"version\": \"v1\"\n  },\n  \"parameters\": {\n    \"to_email\": \"<EMAIL>\",\n    \"to_name\": \"Recipient Name\",\n    \"subject\": \"Test Email from Company Service\",\n    \"message\": \"<h2>Hello!</h2><p>This is a test email from our company email service.</p><p>The system is working perfectly!</p>\",\n    \"message_type\": \"html\"\n  },\n  \"files\": {}\n}"}, "url": {"raw": "{{baseurl}}/api/v1/email/process", "host": ["{{baseurl}}"], "path": ["api", "v1", "email", "process"]}}, "response": []}, {"name": "Send Bulk Email (Adapter Pattern)", "request": {"method": "POST", "header": [{"key": "Content-Type", "value": "application/json"}], "body": {"mode": "raw", "raw": "{\n  \"adapter_config\": {\n    \"service\": \"email_service\",\n    \"operation\": \"send_bulk\",\n    \"version\": \"v1\"\n  },\n  \"parameters\": {\n    \"recipients\": [\n      {\"email\": \"<EMAIL>\", \"name\": \"User One\"},\n      {\"email\": \"<EMAIL>\", \"name\": \"User Two\"},\n      \"<EMAIL>\"\n    ],\n    \"subject\": \"Company Newsletter - Monthly Update\",\n    \"message\": \"<h2>Monthly Newsletter</h2><p>Dear valued customer,</p><p>Here are the latest updates from our company...</p><p>Best regards,<br>Company Team</p>\",\n    \"message_type\": \"html\"\n  },\n  \"files\": {}\n}"}, "url": {"raw": "{{baseurl}}/api/v1/email/process", "host": ["{{baseurl}}"], "path": ["api", "v1", "email", "process"]}}, "response": []}, {"name": "Send Template <PERSON><PERSON> (Adapter Pattern)", "request": {"method": "POST", "header": [{"key": "Content-Type", "value": "application/json"}], "body": {"mode": "raw", "raw": "{\n  \"adapter_config\": {\n    \"service\": \"email_service\",\n    \"operation\": \"send_template\",\n    \"version\": \"v1\"\n  },\n  \"parameters\": {\n    \"template_name\": \"welcome\",\n    \"to_email\": \"<EMAIL>\",\n    \"template_data\": {\n      \"user_name\": \"<PERSON>\",\n      \"email\": \"<EMAIL>\",\n      \"username\": \"johndo<PERSON>\",\n      \"login_url\": \"https://yourcompany.com/login\"\n    }\n  },\n  \"files\": {}\n}"}, "url": {"raw": "{{baseurl}}/api/v1/email/process", "host": ["{{baseurl}}"], "path": ["api", "v1", "email", "process"]}}, "response": []}, {"name": "Send Notification (Adapter Pattern)", "request": {"method": "POST", "header": [{"key": "Content-Type", "value": "application/json"}], "body": {"mode": "raw", "raw": "{\n  \"adapter_config\": {\n    \"service\": \"email_service\",\n    \"operation\": \"send_notification\",\n    \"version\": \"v1\"\n  },\n  \"parameters\": {\n    \"to_email\": \"<EMAIL>\",\n    \"notification_type\": \"login\",\n    \"data\": {\n      \"user_name\": \"<PERSON>\",\n      \"ip_address\": \"*************\",\n      \"message\": \"Successful login detected from new location\"\n    }\n  },\n  \"files\": {}\n}"}, "url": {"raw": "{{baseurl}}/api/v1/email/process", "host": ["{{baseurl}}"], "path": ["api", "v1", "email", "process"]}}, "response": []}, {"name": "Send Welcome Email (Adapter Pattern)", "request": {"method": "POST", "header": [{"key": "Content-Type", "value": "application/json"}], "body": {"mode": "raw", "raw": "{\n  \"adapter_config\": {\n    \"service\": \"email_service\",\n    \"operation\": \"send_welcome\",\n    \"version\": \"v1\"\n  },\n  \"parameters\": {\n    \"to_email\": \"<EMAIL>\",\n    \"user_name\": \"<PERSON>\",\n    \"user_data\": {\n      \"email\": \"<EMAIL>\",\n      \"username\": \"jane<PERSON>\",\n      \"login_url\": \"https://yourcompany.com/login\",\n      \"support_email\": \"<EMAIL>\"\n    }\n  },\n  \"files\": {}\n}"}, "url": {"raw": "{{baseurl}}/api/v1/email/process", "host": ["{{baseurl}}"], "path": ["api", "v1", "email", "process"]}}, "response": []}, {"name": "Send Password Reset (Adapter Pattern)", "request": {"method": "POST", "header": [{"key": "Content-Type", "value": "application/json"}], "body": {"mode": "raw", "raw": "{\n  \"adapter_config\": {\n    \"service\": \"email_service\",\n    \"operation\": \"send_reset\",\n    \"version\": \"v1\"\n  },\n  \"parameters\": {\n    \"to_email\": \"<EMAIL>\",\n    \"user_name\": \"<PERSON>\",\n    \"reset_token\": \"abc123def456ghi789\"\n  },\n  \"files\": {}\n}"}, "url": {"raw": "{{baseurl}}/api/v1/email/process", "host": ["{{baseurl}}"], "path": ["api", "v1", "email", "process"]}}, "response": []}, {"name": "Send Email with Attachments", "request": {"method": "POST", "header": [{"key": "Content-Type", "value": "application/json"}], "body": {"mode": "raw", "raw": "{\n  \"adapter_config\": {\n    \"service\": \"email_service\",\n    \"operation\": \"send_email\",\n    \"version\": \"v1\"\n  },\n  \"parameters\": {\n    \"to_email\": \"<EMAIL>\",\n    \"to_name\": \"Recipient Name\",\n    \"subject\": \"Email with Attachments\",\n    \"message\": \"<h2>Document Attached</h2><p>Please find the attached documents.</p>\",\n    \"message_type\": \"html\"\n  },\n  \"files\": {\n    \"document.txt\": \"VGhpcyBpcyBhIHNhbXBsZSBkb2N1bWVudCBjb250ZW50IGVuY29kZWQgaW4gYmFzZTY0\",\n    \"report.pdf\": \"JVBERi0xLjQKJcOkw7zDtsOgCjIgMCBvYmoKPDwKL0xlbmd0aCAzIDAgUgo+PgpzdHJlYW0K\"\n  }\n}"}, "url": {"raw": "{{baseurl}}/api/v1/email/process", "host": ["{{baseurl}}"], "path": ["api", "v1", "email", "process"]}}, "response": []}, {"name": "Send Email (Simplified)", "request": {"method": "POST", "header": [{"key": "Content-Type", "value": "application/json"}], "body": {"mode": "raw", "raw": "{\n  \"to_email\": \"<EMAIL>\",\n  \"to_name\": \"Recipient Name\",\n  \"subject\": \"Simple Email Test\",\n  \"message\": \"<h2>Hello!</h2><p>This is a simplified email API test.</p>\",\n  \"message_type\": \"html\"\n}"}, "url": {"raw": "{{baseurl}}/api/v1/email/send", "host": ["{{baseurl}}"], "path": ["api", "v1", "email", "send"]}}, "response": []}, {"name": "Send Bulk Email (Simplified)", "request": {"method": "POST", "header": [{"key": "Content-Type", "value": "application/json"}], "body": {"mode": "raw", "raw": "{\n  \"recipients\": [\n    {\"email\": \"<EMAIL>\", \"name\": \"User One\"},\n    {\"email\": \"<EMAIL>\", \"name\": \"User Two\"}\n  ],\n  \"subject\": \"Bulk Email Test\",\n  \"message\": \"<h2>Bulk Email</h2><p>This is a bulk email test.</p>\",\n  \"message_type\": \"html\"\n}"}, "url": {"raw": "{{baseurl}}/api/v1/email/bulk", "host": ["{{baseurl}}"], "path": ["api", "v1", "email", "bulk"]}}, "response": []}, {"name": "Send Template <PERSON><PERSON> (Simplified)", "request": {"method": "POST", "header": [{"key": "Content-Type", "value": "application/json"}], "body": {"mode": "raw", "raw": "{\n  \"template_name\": \"welcome\",\n  \"to_email\": \"<EMAIL>\",\n  \"template_data\": {\n    \"user_name\": \"New User\",\n    \"email\": \"<EMAIL>\",\n    \"username\": \"newuser\",\n    \"login_url\": \"https://yourcompany.com/login\"\n  }\n}"}, "url": {"raw": "{{baseurl}}/api/v1/email/template", "host": ["{{baseurl}}"], "path": ["api", "v1", "email", "template"]}}, "response": []}]}