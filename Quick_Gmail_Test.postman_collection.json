{"info": {"_postman_id": "quick-gmail-test-001", "name": "Quick Gmail Test - <PERSON><PERSON>", "description": "Immediate Gmail opening system - No SMTP setup needed", "schema": "https://schema.getpostman.com/json/collection/v2.1.0/collection.json"}, "variable": [{"key": "baseurl", "value": "http://localhost:5000", "type": "string"}], "item": [{"name": "Health Check", "request": {"method": "GET", "header": [], "url": {"raw": "{{baseurl}}/api/v2/email/health", "host": ["{{baseurl}}"], "path": ["api", "v2", "email", "health"]}}, "response": []}, {"name": "Sivani Direct Login", "request": {"method": "POST", "header": [{"key": "Content-Type", "value": "application/json"}], "body": {"mode": "raw", "raw": "{}"}, "url": {"raw": "{{baseurl}}/api/v2/email/sivani-login", "host": ["{{baseurl}}"], "path": ["api", "v2", "email", "sivani-login"]}}, "response": []}, {"name": "Login Any Email", "request": {"method": "POST", "header": [{"key": "Content-Type", "value": "application/json"}], "body": {"mode": "raw", "raw": "{\n    \"email\": \"<EMAIL>\",\n    \"password\": \"any_password\"\n}"}, "url": {"raw": "{{baseurl}}/api/v2/email/login", "host": ["{{baseurl}}"], "path": ["api", "v2", "email", "login"]}}, "response": []}, {"name": "Open Gmail Directly", "request": {"method": "POST", "header": [{"key": "Content-Type", "value": "application/json"}], "body": {"mode": "raw", "raw": "{}"}, "url": {"raw": "{{baseurl}}/api/v2/email/open-gmail", "host": ["{{baseurl}}"], "path": ["api", "v2", "email", "open-gmail"]}}, "response": []}]}