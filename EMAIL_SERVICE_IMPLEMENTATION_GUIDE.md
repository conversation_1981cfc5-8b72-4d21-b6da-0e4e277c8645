# 🏢 Company Email Service Implementation Guide

## 📋 Overview

This guide provides step-by-step instructions to implement a professional email service system for your company using Python with the exact JSON request format you specified.

## 🎯 System Architecture

```
Client Request → API Gateway → Email Service Adapter → SMTP Provider → Email Delivery
```

### Key Components:
1. **Email Service Core** (`company_email_service.py`)
2. **API Gateway** (`email_service_api.py`)
3. **Configuration Management**
4. **Template System**
5. **Logging & Monitoring**

## 🚀 Implementation Steps

### Step 1: Environment Setup

#### 1.1 Install Dependencies
```bash
pip install flask flask-cors python-dotenv
```

#### 1.2 Create Environment Configuration
Create `.env` file:
```env
# Company Email Configuration
COMPANY_EMAIL=<EMAIL>
COMPANY_PASSWORD=your_app_password_here
COMPANY_NAME=NSL Platform

# SMTP Configuration
SMTP_SERVER=smtp.gmail.com
SMTP_PORT=587
SMTP_USE_TLS=true

# API Configuration
API_HOST=0.0.0.0
API_PORT=7000
DEBUG_MODE=true

# Logging
LOG_LEVEL=INFO
LOG_FILE=email_service.log
```

### Step 2: Core Service Configuration

#### 2.1 Update Email Configuration
Edit `company_email_service.py`:

```python
# Update EmailServiceConfig class
class EmailServiceConfig:
    def __init__(self):
        # Load from environment variables
        self.company_email = os.getenv("COMPANY_EMAIL", "<EMAIL>")
        self.company_password = os.getenv("COMPANY_PASSWORD", "your-app-password")
        self.company_name = os.getenv("COMPANY_NAME", "Your Company")
        
        # SMTP configurations for different providers
        self.smtp_configs = {
            "gmail": {
                "server": "smtp.gmail.com",
                "port": 587,
                "use_tls": True
            },
            "outlook": {
                "server": "smtp-mail.outlook.com",
                "port": 587,
                "use_tls": True
            },
            "company": {
                "server": os.getenv("SMTP_SERVER", "mail.company.com"),
                "port": int(os.getenv("SMTP_PORT", 587)),
                "use_tls": os.getenv("SMTP_USE_TLS", "true").lower() == "true"
            }
        }
```

### Step 3: Request Format Implementation

#### 3.1 Your Specified JSON Format
```json
{
  "adapter_config": {
    "service": "email_service",
    "operation": "send_email",
    "version": "v1"
  },
  "parameters": {
    "to_email": "<EMAIL>",
    "to_name": "Recipient Name",
    "subject": "Email Subject",
    "message": "Email content",
    "message_type": "html"
  },
  "files": {
    "attachment1.pdf": "base64_encoded_file_content",
    "image.png": "base64_encoded_image_content"
  }
}
```

#### 3.2 Supported Operations
- `send_email` - Send single email
- `send_bulk` - Send bulk emails
- `send_template` - Send template-based email
- `send_notification` - Send notification email
- `send_welcome` - Send welcome email to new users
- `send_reset` - Send password reset email

### Step 4: API Endpoints

#### 4.1 Main Adapter Endpoint
```
POST /api/v1/email/process
```
Uses your exact JSON format with adapter pattern.

#### 4.2 Simplified Endpoints
```
POST /api/v1/email/send          - Single email
POST /api/v1/email/bulk          - Bulk emails
POST /api/v1/email/template      - Template email
POST /api/v1/email/notification  - Notification
POST /api/v1/email/welcome       - Welcome email
POST /api/v1/email/reset-password - Password reset
```

### Step 5: Email Templates

#### 5.1 Built-in Templates
- **Welcome Email**: For new user registration
- **Password Reset**: For password recovery
- **Notification**: For general notifications

#### 5.2 Custom Template Creation
```python
def _get_email_template(self, template_name: str, data: Dict[str, Any]):
    templates = {
        "your_template": {
            "subject": "Your Subject with {variable}",
            "message": """
            <html>
            <body>
                <h2>Hello {user_name}!</h2>
                <p>{custom_message}</p>
            </body>
            </html>
            """
        }
    }
    return templates.get(template_name)
```

### Step 6: Security Implementation

#### 6.1 Email Validation
```python
def validate_email(email: str) -> bool:
    import re
    pattern = r'^[a-zA-Z0-9._%+-]+@[a-zA-Z0-9.-]+\.[a-zA-Z]{2,}$'
    return re.match(pattern, email) is not None
```

#### 6.2 Rate Limiting
```python
from flask_limiter import Limiter
from flask_limiter.util import get_remote_address

limiter = Limiter(
    app,
    key_func=get_remote_address,
    default_limits=["100 per hour"]
)

@app.route('/api/v1/email/send', methods=['POST'])
@limiter.limit("10 per minute")
def send_email():
    # Implementation
```

### Step 7: Logging & Monitoring

#### 7.1 Structured Logging
```python
import logging
import json

class EmailServiceLogger:
    def __init__(self):
        logging.basicConfig(
            level=logging.INFO,
            format='%(asctime)s - %(name)s - %(levelname)s - %(message)s',
            handlers=[
                logging.FileHandler('email_service.log'),
                logging.StreamHandler()
            ]
        )
        self.logger = logging.getLogger(__name__)
    
    def log_email_sent(self, to_email: str, subject: str, status: str):
        self.logger.info(json.dumps({
            "event": "email_sent",
            "to_email": to_email,
            "subject": subject,
            "status": status,
            "timestamp": datetime.now().isoformat()
        }))
```

### Step 8: Testing

#### 8.1 Unit Tests
```python
import unittest
from company_email_service import CompanyEmailService

class TestEmailService(unittest.TestCase):
    def setUp(self):
        self.service = CompanyEmailService()
    
    def test_send_email(self):
        request = {
            "adapter_config": {
                "service": "email_service",
                "operation": "send_email",
                "version": "v1"
            },
            "parameters": {
                "to_email": "<EMAIL>",
                "subject": "Test Email",
                "message": "Test message"
            },
            "files": {}
        }
        
        result = self.service.process_request(request)
        self.assertEqual(result["status"], "success")
```

#### 8.2 Integration Tests
```bash
# Test health endpoint
curl -X GET http://localhost:7000/api/v1/email/health

# Test send email
curl -X POST http://localhost:7000/api/v1/email/process \
  -H "Content-Type: application/json" \
  -d '{
    "adapter_config": {
      "service": "email_service",
      "operation": "send_email",
      "version": "v1"
    },
    "parameters": {
      "to_email": "<EMAIL>",
      "subject": "Test Email",
      "message": "Hello from email service!"
    },
    "files": {}
  }'
```

### Step 9: Deployment

#### 9.1 Production Configuration
```python
# production_config.py
import os

class ProductionConfig:
    DEBUG = False
    TESTING = False
    
    # Email settings
    COMPANY_EMAIL = os.getenv("COMPANY_EMAIL")
    COMPANY_PASSWORD = os.getenv("COMPANY_PASSWORD")
    
    # Security
    SECRET_KEY = os.getenv("SECRET_KEY")
    
    # Database (if needed)
    DATABASE_URL = os.getenv("DATABASE_URL")
```

#### 9.2 Docker Deployment
```dockerfile
FROM python:3.9-slim

WORKDIR /app

COPY requirements.txt .
RUN pip install -r requirements.txt

COPY . .

EXPOSE 7000

CMD ["python", "email_service_api.py"]
```

#### 9.3 Docker Compose
```yaml
version: '3.8'
services:
  email-service:
    build: .
    ports:
      - "7000:7000"
    environment:
      - COMPANY_EMAIL=${COMPANY_EMAIL}
      - COMPANY_PASSWORD=${COMPANY_PASSWORD}
      - COMPANY_NAME=${COMPANY_NAME}
    volumes:
      - ./logs:/app/logs
```

### Step 10: Monitoring & Maintenance

#### 10.1 Health Checks
```python
@app.route('/api/v1/email/health', methods=['GET'])
def health_check():
    # Check SMTP connectivity
    # Check template availability
    # Check system resources
    return jsonify({"status": "healthy"})
```

#### 10.2 Metrics Collection
```python
from prometheus_client import Counter, Histogram, generate_latest

email_sent_counter = Counter('emails_sent_total', 'Total emails sent')
email_duration = Histogram('email_send_duration_seconds', 'Email send duration')

@app.route('/metrics')
def metrics():
    return generate_latest()
```

## 🎯 Usage Examples

### Example 1: Send Single Email
```json
{
  "adapter_config": {
    "service": "email_service",
    "operation": "send_email",
    "version": "v1"
  },
  "parameters": {
    "to_email": "<EMAIL>",
    "to_name": "John Doe",
    "subject": "Welcome to Our Service",
    "message": "<h2>Welcome!</h2><p>Thank you for joining us.</p>",
    "message_type": "html"
  },
  "files": {}
}
```

### Example 2: Send Bulk Emails
```json
{
  "adapter_config": {
    "service": "email_service",
    "operation": "send_bulk",
    "version": "v1"
  },
  "parameters": {
    "recipients": [
      {"email": "<EMAIL>", "name": "User 1"},
      {"email": "<EMAIL>", "name": "User 2"}
    ],
    "subject": "Newsletter",
    "message": "<h2>Monthly Newsletter</h2><p>Latest updates...</p>",
    "message_type": "html"
  },
  "files": {}
}
```

### Example 3: Send Template Email
```json
{
  "adapter_config": {
    "service": "email_service",
    "operation": "send_template",
    "version": "v1"
  },
  "parameters": {
    "template_name": "welcome",
    "to_email": "<EMAIL>",
    "template_data": {
      "user_name": "John Doe",
      "username": "johndoe",
      "login_url": "https://yourcompany.com/login"
    }
  },
  "files": {}
}
```

## 🔧 Configuration Checklist

- [ ] Update SMTP credentials
- [ ] Configure company information
- [ ] Set up environment variables
- [ ] Test email sending
- [ ] Configure logging
- [ ] Set up monitoring
- [ ] Implement rate limiting
- [ ] Add security headers
- [ ] Test all endpoints
- [ ] Deploy to production

## 🚨 Security Best Practices

1. **Use App Passwords** for Gmail/Outlook
2. **Encrypt sensitive data** in transit and at rest
3. **Implement rate limiting** to prevent abuse
4. **Validate all inputs** to prevent injection attacks
5. **Use HTTPS** in production
6. **Monitor for suspicious activity**
7. **Regular security audits**

Your company email service is now ready for production use! 🚀
