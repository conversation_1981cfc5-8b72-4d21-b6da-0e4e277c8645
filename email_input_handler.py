import re
import json
import smtplib
import ssl
from email.mime.text import MIMEText
from email.mime.multipart import MIME<PERSON><PERSON><PERSON>art
from email.mime.base import MIMEBase
from email import encoders
import os
from datetime import datetime
from typing import Dict, Any, Optional, Tuple

class EmailInputHandler:
    """
    Email input function handler based on NSL platform patterns
    Handles email validation, processing, and formatting for various use cases
    """
    
    def __init__(self):
        # Email regex pattern for validation
        self.email_pattern = r'^[a-zA-Z0-9._%+-]+@[a-zA-Z0-9.-]+\.[a-zA-Z]{2,}$'

        # Common email domains for additional validation
        self.common_domains = [
            'gmail.com', 'yahoo.com', 'outlook.com', 'hotmail.com',
            'braeneterprises.com', 'example.com'
        ]

        # SMTP Configuration
        self.smtp_config = {
            'gmail': {
                'server': 'smtp.gmail.com',
                'port': 587,
                'use_tls': True
            },
            'outlook': {
                'server': 'smtp-mail.outlook.com',
                'port': 587,
                'use_tls': True
            },
            'yahoo': {
                'server': 'smtp.mail.yahoo.com',
                'port': 587,
                'use_tls': True
            }
        }
    
    def validate_email(self, email: str) -> Tuple[bool, str]:
        """
        Validate email format and structure
        
        Args:
            email (str): Email address to validate
            
        Returns:
            Tuple[bool, str]: (is_valid, error_message)
        """
        if not email:
            return False, "Email is required"
        
        if not isinstance(email, str):
            return False, "Email must be a string"
        
        # Remove whitespace
        email = email.strip()
        
        # Check length
        if len(email) > 254:
            return False, "Email address is too long"
        
        # Check basic format
        if not re.match(self.email_pattern, email):
            return False, "Invalid email format"
        
        # Check for multiple @ symbols
        if email.count('@') != 1:
            return False, "Email must contain exactly one @ symbol"
        
        # Split local and domain parts
        local_part, domain_part = email.split('@')
        
        # Validate local part
        if len(local_part) > 64:
            return False, "Local part of email is too long"
        
        if not local_part:
            return False, "Local part of email cannot be empty"
        
        # Validate domain part
        if len(domain_part) > 253:
            return False, "Domain part of email is too long"
        
        if not domain_part:
            return False, "Domain part of email cannot be empty"
        
        return True, "Valid email"
    
    def process_email_input(self, email_data: Dict[str, Any]) -> Dict[str, Any]:
        """
        Process email input similar to NSL platform patterns
        
        Args:
            email_data (Dict): Input data containing email and context
            
        Returns:
            Dict: Processed result with validation status and formatted data
        """
        result = {
            "status": "error",
            "message": "",
            "data": {},
            "email": "",
            "is_valid": False
        }
        
        try:
            # Extract email from input data
            email = email_data.get('email', '').strip().lower()
            
            if not email:
                result["message"] = "Email field is required"
                return result
            
            # Validate email
            is_valid, validation_message = self.validate_email(email)
            
            if not is_valid:
                result["message"] = validation_message
                return result
            
            # Process based on context (register, login, profile update, etc.)
            context = email_data.get('context', 'general')
            
            if context == 'register':
                return self._process_registration_email(email, email_data)
            elif context == 'login':
                return self._process_login_email(email, email_data)
            elif context == 'profile':
                return self._process_profile_email(email, email_data)
            else:
                return self._process_general_email(email, email_data)
                
        except Exception as e:
            result["message"] = f"Error processing email input: {str(e)}"
            return result
    
    def _process_registration_email(self, email: str, data: Dict[str, Any]) -> Dict[str, Any]:
        """Process email for user registration"""
        return {
            "status": "success",
            "message": "Email validated for registration",
            "data": {
                "email": email,
                "username": data.get('username', email.split('@')[0]),
                "first_name": data.get('first_name', ''),
                "last_name": data.get('last_name', ''),
                "tenant_id": data.get('tenant_id', 't001'),
                "roles": data.get('roles', ['User'])
            },
            "email": email,
            "is_valid": True,
            "context": "register"
        }
    
    def _process_login_email(self, email: str, data: Dict[str, Any]) -> Dict[str, Any]:
        """Process email for user login"""
        return {
            "status": "success",
            "message": "Email validated for login",
            "data": {
                "username": email,  # Email used as username
                "password": data.get('password', ''),
                "email": email
            },
            "email": email,
            "is_valid": True,
            "context": "login"
        }
    
    def _process_profile_email(self, email: str, data: Dict[str, Any]) -> Dict[str, Any]:
        """Process email for profile updates"""
        return {
            "status": "success",
            "message": "Email validated for profile update",
            "data": {
                "email": email,
                "user_id": data.get('user_id', ''),
                "first_name": data.get('first_name', ''),
                "last_name": data.get('last_name', ''),
                "tenant_id": data.get('tenant_id', 't001')
            },
            "email": email,
            "is_valid": True,
            "context": "profile"
        }
    
    def _process_general_email(self, email: str, data: Dict[str, Any]) -> Dict[str, Any]:
        """Process email for general use"""
        return {
            "status": "success",
            "message": "Email validated successfully",
            "data": {
                "email": email,
                "formatted_email": email,
                "domain": email.split('@')[1],
                "local_part": email.split('@')[0]
            },
            "email": email,
            "is_valid": True,
            "context": "general"
        }
    
    def format_for_api(self, email_result: Dict[str, Any], api_format: str = 'json') -> str:
        """
        Format email processing result for API responses
        
        Args:
            email_result (Dict): Result from process_email_input
            api_format (str): Output format ('json', 'form_data')
            
        Returns:
            str: Formatted output
        """
        if api_format == 'json':
            return json.dumps(email_result, indent=2)
        elif api_format == 'form_data':
            # Format for form data submission
            form_data = []
            for key, value in email_result.get('data', {}).items():
                if isinstance(value, list):
                    for item in value:
                        form_data.append(f"{key}={item}")
                else:
                    form_data.append(f"{key}={value}")
            return "&".join(form_data)
        else:
            return str(email_result)

    def send_email(self, sender_email: str, sender_password: str, recipient_email: str,
                   subject: str, message: str, email_type: str = 'html') -> Dict[str, Any]:
        """
        Send email using SMTP

        Args:
            sender_email (str): Sender's email address
            sender_password (str): Sender's email password or app password
            recipient_email (str): Recipient's email address
            subject (str): Email subject
            message (str): Email message content
            email_type (str): 'html' or 'plain'

        Returns:
            Dict: Result of email sending operation
        """
        try:
            # Validate sender and recipient emails
            sender_valid, sender_msg = self.validate_email(sender_email)
            if not sender_valid:
                return {
                    "status": "error",
                    "message": f"Invalid sender email: {sender_msg}",
                    "sent": False
                }

            recipient_valid, recipient_msg = self.validate_email(recipient_email)
            if not recipient_valid:
                return {
                    "status": "error",
                    "message": f"Invalid recipient email: {recipient_msg}",
                    "sent": False
                }

            # Determine SMTP configuration based on sender domain
            sender_domain = sender_email.split('@')[1].lower()
            smtp_config = None

            if 'gmail' in sender_domain:
                smtp_config = self.smtp_config['gmail']
            elif 'outlook' in sender_domain or 'hotmail' in sender_domain:
                smtp_config = self.smtp_config['outlook']
            elif 'yahoo' in sender_domain:
                smtp_config = self.smtp_config['yahoo']
            else:
                # Default to Gmail configuration
                smtp_config = self.smtp_config['gmail']

            # Create message
            msg = MIMEMultipart()
            msg['From'] = sender_email
            msg['To'] = recipient_email
            msg['Subject'] = subject

            # Add body to email
            if email_type.lower() == 'html':
                msg.attach(MIMEText(message, 'html'))
            else:
                msg.attach(MIMEText(message, 'plain'))

            # Create SMTP session
            server = smtplib.SMTP(smtp_config['server'], smtp_config['port'])

            if smtp_config['use_tls']:
                server.starttls()  # Enable security

            server.login(sender_email, sender_password)
            text = msg.as_string()
            server.sendmail(sender_email, recipient_email, text)
            server.quit()

            return {
                "status": "success",
                "message": "Email sent successfully",
                "sent": True,
                "sender": sender_email,
                "recipient": recipient_email,
                "subject": subject,
                "timestamp": datetime.now().isoformat()
            }

        except Exception as e:
            return {
                "status": "error",
                "message": f"Failed to send email: {str(e)}",
                "sent": False,
                "error_details": str(e)
            }

    def send_welcome_email(self, sender_email: str, sender_password: str,
                          recipient_email: str, user_name: str) -> Dict[str, Any]:
        """Send welcome email for new user registration"""
        subject = "Welcome to NSL Platform!"

        html_message = f"""
        <html>
        <body>
            <h2>Welcome to NSL Platform, {user_name}!</h2>
            <p>Thank you for registering with us. Your account has been successfully created.</p>
            <p><strong>Your login details:</strong></p>
            <ul>
                <li>Email: {recipient_email}</li>
                <li>Login URL: <a href="http://**********:8000/api/v2/auth/login">NSL Platform Login</a></li>
            </ul>
            <p>If you have any questions, please don't hesitate to contact our support team.</p>
            <p>Best regards,<br>NSL Platform Team</p>
        </body>
        </html>
        """

        return self.send_email(sender_email, sender_password, recipient_email,
                              subject, html_message, 'html')

    def send_login_notification(self, sender_email: str, sender_password: str,
                               recipient_email: str, user_name: str) -> Dict[str, Any]:
        """Send login notification email"""
        subject = "NSL Platform - Login Notification"

        html_message = f"""
        <html>
        <body>
            <h2>Login Notification</h2>
            <p>Hello {user_name},</p>
            <p>We detected a login to your NSL Platform account.</p>
            <p><strong>Login Details:</strong></p>
            <ul>
                <li>Email: {recipient_email}</li>
                <li>Time: {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}</li>
                <li>Platform: NSL Platform</li>
            </ul>
            <p>If this wasn't you, please contact our support team immediately.</p>
            <p>Best regards,<br>NSL Platform Security Team</p>
        </body>
        </html>
        """

        return self.send_email(sender_email, sender_password, recipient_email,
                              subject, html_message, 'html')

    def send_password_reset(self, sender_email: str, sender_password: str,
                           recipient_email: str, reset_token: str) -> Dict[str, Any]:
        """Send password reset email"""
        subject = "NSL Platform - Password Reset Request"

        reset_url = f"http://**********:8000/api/v2/auth/reset-password?token={reset_token}"

        html_message = f"""
        <html>
        <body>
            <h2>Password Reset Request</h2>
            <p>You have requested to reset your password for NSL Platform.</p>
            <p>Click the link below to reset your password:</p>
            <p><a href="{reset_url}" style="background-color: #007bff; color: white; padding: 10px 20px; text-decoration: none; border-radius: 5px;">Reset Password</a></p>
            <p>If you didn't request this, please ignore this email.</p>
            <p>This link will expire in 24 hours.</p>
            <p>Best regards,<br>NSL Platform Team</p>
        </body>
        </html>
        """

        return self.send_email(sender_email, sender_password, recipient_email,
                              subject, html_message, 'html')

# Example usage functions based on NSL platform patterns
def register_user_email_input(email: str, username: str = "", first_name: str = "", 
                             last_name: str = "", tenant_id: str = "t001") -> Dict[str, Any]:
    """
    Example function for user registration email input
    Similar to: {"username": "testuser12", "email": "<EMAIL>", ...}
    """
    handler = EmailInputHandler()
    
    email_data = {
        "email": email,
        "username": username,
        "first_name": first_name,
        "last_name": last_name,
        "tenant_id": tenant_id,
        "roles": ["User"],
        "context": "register"
    }
    
    return handler.process_email_input(email_data)

def login_email_input(email: str, password: str = "") -> Dict[str, Any]:
    """
    Example function for login email input
    Similar to: {"username": "<EMAIL>", "password": "sivani@123"}
    """
    handler = EmailInputHandler()
    
    email_data = {
        "email": email,
        "password": password,
        "context": "login"
    }
    
    return handler.process_email_input(email_data)

# Test function to demonstrate usage
if __name__ == "__main__":
    # Test registration email input
    print("=== Registration Email Test ===")
    reg_result = register_user_email_input(
        email="<EMAIL>",
        username="sivani dornadula",
        first_name="Sivani",
        last_name="Dornadula"
    )
    print(json.dumps(reg_result, indent=2))
    
    print("\n=== Login Email Test ===")
    login_result = login_email_input(
        email="<EMAIL>",
        password="Sivani@123"
    )
    print(json.dumps(login_result, indent=2))
    
    print("\n=== Invalid Email Test ===")
    invalid_result = register_user_email_input(email="invalid-email")
    print(json.dumps(invalid_result, indent=2))
