{"info": {"_postman_id": "gmail-direct-system-001", "name": "Gmail Direct System - Sivani", "description": "Direct Gmail access <NAME_EMAIL>", "schema": "https://schema.getpostman.com/json/collection/v2.1.0/collection.json"}, "variable": [{"key": "baseurl", "value": "http://localhost:5000", "type": "string"}, {"key": "gmail_email", "value": "<EMAIL>", "type": "string"}], "item": [{"name": "Health Check", "request": {"method": "GET", "header": [], "url": {"raw": "{{baseurl}}/api/v2/email/health", "host": ["{{baseurl}}"], "path": ["api", "v2", "email", "health"]}}, "response": []}, {"name": "Login and Open Gmail", "request": {"method": "POST", "header": [{"key": "Content-Type", "value": "application/json"}], "body": {"mode": "raw", "raw": "{\n    \"email\": \"<EMAIL>\",\n    \"password\": \"test123\",\n    \"open_gmail\": true\n}"}, "url": {"raw": "{{baseurl}}/api/v2/email/login", "host": ["{{baseurl}}"], "path": ["api", "v2", "email", "login"]}}, "response": []}, {"name": "<PERSON><PERSON>", "request": {"method": "POST", "header": [{"key": "Content-Type", "value": "application/json"}], "body": {"mode": "raw", "raw": "{\n    \"email\": \"{{gmail_email}}\",\n    \"password\": \"<PERSON>vani@123\",\n    \"open_gmail\": true\n}"}, "url": {"raw": "{{baseurl}}/api/v2/email/login", "host": ["{{baseurl}}"], "path": ["api", "v2", "email", "login"]}}, "response": []}, {"name": "Send Email and Open Gmail", "request": {"method": "POST", "header": [{"key": "Content-Type", "value": "application/json"}], "body": {"mode": "raw", "raw": "{\n    \"recipient_email\": \"<EMAIL>\",\n    \"subject\": \"Test Email from NSL Platform\",\n    \"message\": \"<h2>Hello!</h2><p>This is a test email sent from NSL Platform API.</p><p>Gmail will open automatically after sending this email.</p>\",\n    \"open_gmail\": true\n}"}, "url": {"raw": "{{baseurl}}/api/v2/email/send-and-open", "host": ["{{baseurl}}"], "path": ["api", "v2", "email", "send-and-open"]}}, "response": []}, {"name": "Open Gmail Directly", "request": {"method": "POST", "header": [{"key": "Content-Type", "value": "application/json"}], "body": {"mode": "raw", "raw": "{}"}, "url": {"raw": "{{baseurl}}/api/v2/email/open-gmail", "host": ["{{baseurl}}"], "path": ["api", "v2", "email", "open-gmail"]}}, "response": []}, {"name": "Send to Sivani Email", "request": {"method": "POST", "header": [{"key": "Content-Type", "value": "application/json"}], "body": {"mode": "raw", "raw": "{\n    \"recipient_email\": \"{{gmail_email}}\",\n    \"subject\": \"NSL Platform - Login Notification\",\n    \"message\": \"<h2>Welcome to NSL Platform!</h2><p>Your login was successful.</p><p>Email: {{gmail_email}}</p><p>Time: $(new Date().toLocaleString())</p>\",\n    \"open_gmail\": true\n}"}, "url": {"raw": "{{baseurl}}/api/v2/email/send-and-open", "host": ["{{baseurl}}"], "path": ["api", "v2", "email", "send-and-open"]}}, "response": []}]}