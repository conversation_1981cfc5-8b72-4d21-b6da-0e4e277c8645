#!/usr/bin/env python3
"""
Email Input Function Example
Based on NSL Platform patterns

This script demonstrates how to use the email input handler
similar to your system's API endpoints.
"""

from email_input_handler import EmailInputHandler, register_user_email_input, login_email_input
import json

def main():
    """Main function to demonstrate email input processing"""
    
    print("=" * 60)
    print("NSL PLATFORM - EMAIL INPUT FUNCTION DEMO")
    print("=" * 60)
    
    # Initialize the email handler
    handler = EmailInputHandler()
    
    # Example 1: Registration Email Input (like your register endpoint)
    print("\n1. REGISTRATION EMAIL INPUT")
    print("-" * 30)
    
    registration_data = {
        "email": "<EMAIL>",
        "username": "testuser12",
        "first_name": "Durga",
        "last_name": "<PERSON>",
        "tenant_id": "t001",
        "roles": ["User"],
        "context": "register"
    }
    
    reg_result = handler.process_email_input(registration_data)
    print("Input:", json.dumps(registration_data, indent=2))
    print("Output:", json.dumps(reg_result, indent=2))
    
    # Example 2: Login Email Input (like your login with email endpoint)
    print("\n2. LOGIN EMAIL INPUT")
    print("-" * 30)
    
    login_data = {
        "email": "<EMAIL>",
        "password": "Sivani@123",
        "context": "login"
    }
    
    login_result = handler.process_email_input(login_data)
    print("Input:", json.dumps(login_data, indent=2))
    print("Output:", json.dumps(login_result, indent=2))
    
    # Example 3: Profile Update Email Input
    print("\n3. PROFILE UPDATE EMAIL INPUT")
    print("-" * 30)
    
    profile_data = {
        "email": "<EMAIL>",
        "user_id": "NH3426",
        "first_name": "Sivani",
        "last_name": "Dornadula",
        "tenant_id": "001",
        "context": "profile"
    }
    
    profile_result = handler.process_email_input(profile_data)
    print("Input:", json.dumps(profile_data, indent=2))
    print("Output:", json.dumps(profile_result, indent=2))
    
    # Example 4: Invalid Email Input
    print("\n4. INVALID EMAIL INPUT")
    print("-" * 30)
    
    invalid_data = {
        "email": "invalid-email-format",
        "context": "register"
    }
    
    invalid_result = handler.process_email_input(invalid_data)
    print("Input:", json.dumps(invalid_data, indent=2))
    print("Output:", json.dumps(invalid_result, indent=2))
    
    # Example 5: API Format Output
    print("\n5. API FORMAT OUTPUT")
    print("-" * 30)
    
    # JSON format (default)
    json_output = handler.format_for_api(reg_result, 'json')
    print("JSON Format:")
    print(json_output)
    
    # Form data format
    form_output = handler.format_for_api(reg_result, 'form_data')
    print("\nForm Data Format:")
    print(form_output)
    
    # Example 6: Using convenience functions
    print("\n6. CONVENIENCE FUNCTIONS")
    print("-" * 30)
    
    # Quick registration
    quick_reg = register_user_email_input(
        email="<EMAIL>",
        username="sivani",
        first_name="Sivani",
        last_name="Dornadula"
    )
    print("Quick Registration:")
    print(json.dumps(quick_reg, indent=2))
    
    # Quick login
    quick_login = login_email_input(
        email="<EMAIL>",
        password="Sivani@123"
    )
    print("\nQuick Login:")
    print(json.dumps(quick_login, indent=2))
    
    print("\n" + "=" * 60)
    print("DEMO COMPLETED")
    print("=" * 60)

def test_email_validation():
    """Test various email validation scenarios"""
    
    print("\n" + "=" * 60)
    print("EMAIL VALIDATION TESTS")
    print("=" * 60)
    
    handler = EmailInputHandler()
    
    test_emails = [
        "<EMAIL>",
        "<EMAIL>",
        "invalid-email",
        "@domain.com",
        "user@",
        "user@domain",
        "user <EMAIL>",
        "<EMAIL>",
        ""
       
    ]
    
    for email in test_emails:
        is_valid, message = handler.validate_email(email)
        status = "✓ VALID" if is_valid else "✗ INVALID"
        print(f"{status:10} | {email:30} | {message}")

if __name__ == "__main__":
    main()
    test_email_validation()
