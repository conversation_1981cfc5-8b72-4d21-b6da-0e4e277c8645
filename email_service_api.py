#!/usr/bin/env python3
"""
Company Email Service API
Flask API wrapper for the company email service
"""

from flask import Flask, request, jsonify
from flask_cors import CORS
import json
import logging
from datetime import datetime
from company_email_service import CompanyEmailService

# Configure logging
logging.basicConfig(level=logging.INFO)
logger = logging.getLogger(__name__)

app = Flask(__name__)
CORS(app)

# Initialize email service
email_service = CompanyEmailService()

@app.route('/api/v1/email/health', methods=['GET'])
def health_check():
    """Health check endpoint"""
    return jsonify({
        "status": "success",
        "message": "Company Email Service API is running",
        "service": "email_service",
        "version": "v1",
        "timestamp": datetime.now().isoformat(),
        "endpoints": {
            "POST /api/v1/email/process": "Process email service requests",
            "POST /api/v1/email/send": "Send single email (simplified)",
            "POST /api/v1/email/bulk": "Send bulk emails (simplified)",
            "POST /api/v1/email/template": "Send template email (simplified)"
        }
    })

@app.route('/api/v1/email/process', methods=['POST'])
def process_email_request():
    """Process email service request with adapter pattern"""
    try:
        # Get request data
        request_data = request.get_json()
        
        if not request_data:
            return jsonify({
                "status": "error",
                "message": "No JSON data provided",
                "timestamp": datetime.now().isoformat()
            }), 400
        
        # Log request
        logger.info(f"Processing email request: {request_data.get('adapter_config', {}).get('operation', 'unknown')}")
        
        # Process request through email service
        result = email_service.process_request(request_data)
        
        # Return appropriate HTTP status
        status_code = 200 if result.get("status") == "success" else 400
        
        return jsonify(result), status_code
        
    except Exception as e:
        logger.error(f"Error processing email request: {str(e)}")
        return jsonify({
            "status": "error",
            "message": f"Internal server error: {str(e)}",
            "timestamp": datetime.now().isoformat()
        }), 500

@app.route('/api/v1/email/send', methods=['POST'])
def send_email_simplified():
    """Simplified send email endpoint"""
    try:
        data = request.get_json()
        
        # Convert to adapter format
        adapter_request = {
            "adapter_config": {
                "service": "email_service",
                "operation": "send_email",
                "version": "v1"
            },
            "parameters": {
                "to_email": data.get("to_email"),
                "to_name": data.get("to_name", ""),
                "subject": data.get("subject", ""),
                "message": data.get("message", ""),
                "message_type": data.get("message_type", "html"),
                "from_email": data.get("from_email"),
                "from_name": data.get("from_name")
            },
            "files": data.get("files", {})
        }
        
        result = email_service.process_request(adapter_request)
        status_code = 200 if result.get("status") == "success" else 400
        
        return jsonify(result), status_code
        
    except Exception as e:
        logger.error(f"Error in simplified send email: {str(e)}")
        return jsonify({
            "status": "error",
            "message": str(e),
            "timestamp": datetime.now().isoformat()
        }), 500

@app.route('/api/v1/email/bulk', methods=['POST'])
def send_bulk_email_simplified():
    """Simplified bulk email endpoint"""
    try:
        data = request.get_json()
        
        # Convert to adapter format
        adapter_request = {
            "adapter_config": {
                "service": "email_service",
                "operation": "send_bulk",
                "version": "v1"
            },
            "parameters": {
                "recipients": data.get("recipients", []),
                "subject": data.get("subject", ""),
                "message": data.get("message", ""),
                "message_type": data.get("message_type", "html")
            },
            "files": data.get("files", {})
        }
        
        result = email_service.process_request(adapter_request)
        status_code = 200 if result.get("status") == "success" else 400
        
        return jsonify(result), status_code
        
    except Exception as e:
        logger.error(f"Error in bulk email: {str(e)}")
        return jsonify({
            "status": "error",
            "message": str(e),
            "timestamp": datetime.now().isoformat()
        }), 500

@app.route('/api/v1/email/template', methods=['POST'])
def send_template_email_simplified():
    """Simplified template email endpoint"""
    try:
        data = request.get_json()
        
        # Convert to adapter format
        adapter_request = {
            "adapter_config": {
                "service": "email_service",
                "operation": "send_template",
                "version": "v1"
            },
            "parameters": {
                "template_name": data.get("template_name"),
                "to_email": data.get("to_email"),
                "template_data": data.get("template_data", {})
            },
            "files": data.get("files", {})
        }
        
        result = email_service.process_request(adapter_request)
        status_code = 200 if result.get("status") == "success" else 400
        
        return jsonify(result), status_code
        
    except Exception as e:
        logger.error(f"Error in template email: {str(e)}")
        return jsonify({
            "status": "error",
            "message": str(e),
            "timestamp": datetime.now().isoformat()
        }), 500

@app.route('/api/v1/email/notification', methods=['POST'])
def send_notification_simplified():
    """Simplified notification endpoint"""
    try:
        data = request.get_json()
        
        # Convert to adapter format
        adapter_request = {
            "adapter_config": {
                "service": "email_service",
                "operation": "send_notification",
                "version": "v1"
            },
            "parameters": {
                "to_email": data.get("to_email"),
                "notification_type": data.get("notification_type", "general"),
                "data": data.get("data", {})
            },
            "files": data.get("files", {})
        }
        
        result = email_service.process_request(adapter_request)
        status_code = 200 if result.get("status") == "success" else 400
        
        return jsonify(result), status_code
        
    except Exception as e:
        logger.error(f"Error in notification email: {str(e)}")
        return jsonify({
            "status": "error",
            "message": str(e),
            "timestamp": datetime.now().isoformat()
        }), 500

@app.route('/api/v1/email/welcome', methods=['POST'])
def send_welcome_email_simplified():
    """Simplified welcome email endpoint"""
    try:
        data = request.get_json()
        
        # Convert to adapter format
        adapter_request = {
            "adapter_config": {
                "service": "email_service",
                "operation": "send_welcome",
                "version": "v1"
            },
            "parameters": {
                "to_email": data.get("to_email"),
                "user_name": data.get("user_name"),
                "user_data": data.get("user_data", {})
            },
            "files": data.get("files", {})
        }
        
        result = email_service.process_request(adapter_request)
        status_code = 200 if result.get("status") == "success" else 400
        
        return jsonify(result), status_code
        
    except Exception as e:
        logger.error(f"Error in welcome email: {str(e)}")
        return jsonify({
            "status": "error",
            "message": str(e),
            "timestamp": datetime.now().isoformat()
        }), 500

@app.route('/api/v1/email/reset-password', methods=['POST'])
def send_reset_password_simplified():
    """Simplified password reset email endpoint"""
    try:
        data = request.get_json()
        
        # Convert to adapter format
        adapter_request = {
            "adapter_config": {
                "service": "email_service",
                "operation": "send_reset",
                "version": "v1"
            },
            "parameters": {
                "to_email": data.get("to_email"),
                "user_name": data.get("user_name"),
                "reset_token": data.get("reset_token")
            },
            "files": data.get("files", {})
        }
        
        result = email_service.process_request(adapter_request)
        status_code = 200 if result.get("status") == "success" else 400
        
        return jsonify(result), status_code
        
    except Exception as e:
        logger.error(f"Error in password reset email: {str(e)}")
        return jsonify({
            "status": "error",
            "message": str(e),
            "timestamp": datetime.now().isoformat()
        }), 500

@app.errorhandler(404)
def not_found(error):
    """Handle 404 errors"""
    return jsonify({
        "status": "error",
        "message": "Endpoint not found",
        "timestamp": datetime.now().isoformat()
    }), 404

@app.errorhandler(500)
def internal_error(error):
    """Handle 500 errors"""
    return jsonify({
        "status": "error",
        "message": "Internal server error",
        "timestamp": datetime.now().isoformat()
    }), 500

if __name__ == '__main__':
    print("=" * 70)
    print("🏢 COMPANY EMAIL SERVICE API")
    print("=" * 70)
    print("📧 Service: email_service")
    print("🌐 Server: http://localhost:7000")
    print("📋 Health Check: http://localhost:7000/api/v1/email/health")
    print("=" * 70)
    print("🎯 Main Endpoint:")
    print("   POST /api/v1/email/process")
    print("   - Uses adapter pattern with your specified JSON format")
    print("=" * 70)
    print("🚀 Simplified Endpoints:")
    print("   POST /api/v1/email/send - Send single email")
    print("   POST /api/v1/email/bulk - Send bulk emails")
    print("   POST /api/v1/email/template - Send template email")
    print("   POST /api/v1/email/notification - Send notification")
    print("   POST /api/v1/email/welcome - Send welcome email")
    print("   POST /api/v1/email/reset-password - Send password reset")
    print("=" * 70)
    print("🔧 Configuration:")
    print("   Update company_email_service.py with your SMTP settings")
    print("=" * 70)
    print("🚀 Starting server...")
    print("=" * 70)
    
    app.run(debug=True, host='0.0.0.0', port=7000)
