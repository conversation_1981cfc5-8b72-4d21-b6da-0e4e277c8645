#!/usr/bin/env python3
"""
NSL Platform Email API
Flask API for email operations with SMTP integration
"""

from flask import Flask, request, jsonify
from flask_cors import CORS
import json
import os
from datetime import datetime
import uuid
from email_input_handler import EmailInputHandler, register_user_email_input, login_email_input

app = Flask(__name__)
CORS(app)  # Enable CORS for all routes

# Initialize email handler
email_handler = EmailInputHandler()

# Configuration
EMAIL_CONFIG = {
    "smtp_email": os.getenv("SMTP_EMAIL", "<EMAIL>"),
    "smtp_password": os.getenv("SMTP_PASSWORD", "your-app-password"),
    "default_sender": os.getenv("DEFAULT_SENDER", "<EMAIL>")
}

@app.route('/api/v2/email/health', methods=['GET'])
def health_check():
    """Health check endpoint"""
    return jsonify({
        "status": "success",
        "message": "NSL Email API is running",
        "timestamp": datetime.now().isoformat(),
        "version": "2.0"
    })

@app.route('/api/v2/email/validate', methods=['POST'])
def validate_email():
    """
    Validate email input
    POST /api/v2/email/validate
    Body: {"email": "<EMAIL>", "context": "register"}
    """
    try:
        data = request.get_json()
        
        if not data:
            return jsonify({
                "status": "error",
                "message": "No JSON data provided"
            }), 400
        
        email = data.get('email', '').strip()
        context = data.get('context', 'general')
        
        if not email:
            return jsonify({
                "status": "error",
                "message": "Email is required"
            }), 400
        
        # Validate email
        is_valid, message = email_handler.validate_email(email)
        
        if not is_valid:
            return jsonify({
                "status": "error",
                "message": message,
                "is_valid": False
            }), 400
        
        # Process email based on context
        email_data = data.copy()
        result = email_handler.process_email_input(email_data)
        
        return jsonify(result)
        
    except Exception as e:
        return jsonify({
            "status": "error",
            "message": f"Error validating email: {str(e)}"
        }), 500

@app.route('/api/v2/email/send', methods=['POST'])
def send_email():
    """
    Send email via SMTP
    POST /api/v2/email/send
    Body: {
        "sender_email": "<EMAIL>",
        "sender_password": "app-password",
        "recipient_email": "<EMAIL>",
        "subject": "Test Email",
        "message": "Hello World",
        "email_type": "html"
    }
    """
    try:
        data = request.get_json()
        
        if not data:
            return jsonify({
                "status": "error",
                "message": "No JSON data provided"
            }), 400
        
        # Required fields
        required_fields = ['sender_email', 'sender_password', 'recipient_email', 'subject', 'message']
        for field in required_fields:
            if not data.get(field):
                return jsonify({
                    "status": "error",
                    "message": f"Field '{field}' is required"
                }), 400
        
        # Send email
        result = email_handler.send_email(
            sender_email=data['sender_email'],
            sender_password=data['sender_password'],
            recipient_email=data['recipient_email'],
            subject=data['subject'],
            message=data['message'],
            email_type=data.get('email_type', 'html')
        )
        
        if result['status'] == 'success':
            return jsonify(result)
        else:
            return jsonify(result), 400
            
    except Exception as e:
        return jsonify({
            "status": "error",
            "message": f"Error sending email: {str(e)}"
        }), 500

@app.route('/api/v2/email/register', methods=['POST'])
def register_user():
    """
    Register user and send welcome email
    POST /api/v2/email/register
    Body: {
        "email": "<EMAIL>",
        "username": "user123",
        "first_name": "John",
        "last_name": "Doe",
        "password": "secure123",
        "tenant_id": "t001",
        "sender_email": "<EMAIL>",
        "sender_password": "app-password"
    }
    """
    try:
        data = request.get_json()
        
        if not data:
            return jsonify({
                "status": "error",
                "message": "No JSON data provided"
            }), 400
        
        # Validate registration data
        reg_result = register_user_email_input(
            email=data.get('email', ''),
            username=data.get('username', ''),
            first_name=data.get('first_name', ''),
            last_name=data.get('last_name', ''),
            tenant_id=data.get('tenant_id', 't001')
        )
        
        if reg_result['status'] != 'success':
            return jsonify(reg_result), 400
        
        # Send welcome email if sender credentials provided
        if data.get('sender_email') and data.get('sender_password'):
            user_name = f"{data.get('first_name', '')} {data.get('last_name', '')}".strip()
            if not user_name:
                user_name = data.get('username', 'User')
            
            email_result = email_handler.send_welcome_email(
                sender_email=data['sender_email'],
                sender_password=data['sender_password'],
                recipient_email=data['email'],
                user_name=user_name
            )
            
            reg_result['email_sent'] = email_result
        
        return jsonify(reg_result)
        
    except Exception as e:
        return jsonify({
            "status": "error",
            "message": f"Error in user registration: {str(e)}"
        }), 500

@app.route('/api/v2/email/login', methods=['POST'])
def login_user():
    """
    Login user and send notification email
    POST /api/v2/email/login
    Body: {
        "email": "<EMAIL>",
        "password": "secure123",
        "sender_email": "<EMAIL>",
        "sender_password": "app-password",
        "send_notification": true
    }
    """
    try:
        data = request.get_json()
        
        if not data:
            return jsonify({
                "status": "error",
                "message": "No JSON data provided"
            }), 400
        
        # Validate login data
        login_result = login_email_input(
            email=data.get('email', ''),
            password=data.get('password', '')
        )
        
        if login_result['status'] != 'success':
            return jsonify(login_result), 400
        
        # Send login notification if requested and sender credentials provided
        if (data.get('send_notification', False) and 
            data.get('sender_email') and data.get('sender_password')):
            
            user_name = data.get('username', data.get('email', 'User'))
            
            email_result = email_handler.send_login_notification(
                sender_email=data['sender_email'],
                sender_password=data['sender_password'],
                recipient_email=data['email'],
                user_name=user_name
            )
            
            login_result['email_sent'] = email_result
        
        # Add login token (simulate)
        login_result['token'] = str(uuid.uuid4())
        login_result['expires_in'] = 3600  # 1 hour
        
        return jsonify(login_result)
        
    except Exception as e:
        return jsonify({
            "status": "error",
            "message": f"Error in user login: {str(e)}"
        }), 500

@app.route('/api/v2/email/reset-password', methods=['POST'])
def reset_password():
    """
    Send password reset email
    POST /api/v2/email/reset-password
    Body: {
        "email": "<EMAIL>",
        "sender_email": "<EMAIL>",
        "sender_password": "app-password"
    }
    """
    try:
        data = request.get_json()
        
        if not data:
            return jsonify({
                "status": "error",
                "message": "No JSON data provided"
            }), 400
        
        email = data.get('email', '').strip()
        if not email:
            return jsonify({
                "status": "error",
                "message": "Email is required"
            }), 400
        
        # Validate email
        is_valid, message = email_handler.validate_email(email)
        if not is_valid:
            return jsonify({
                "status": "error",
                "message": message
            }), 400
        
        # Generate reset token
        reset_token = str(uuid.uuid4())
        
        # Send reset email if sender credentials provided
        if data.get('sender_email') and data.get('sender_password'):
            email_result = email_handler.send_password_reset(
                sender_email=data['sender_email'],
                sender_password=data['sender_password'],
                recipient_email=email,
                reset_token=reset_token
            )
            
            return jsonify({
                "status": "success",
                "message": "Password reset email sent",
                "email": email,
                "reset_token": reset_token,
                "email_sent": email_result
            })
        else:
            return jsonify({
                "status": "success",
                "message": "Password reset token generated",
                "email": email,
                "reset_token": reset_token,
                "note": "No sender credentials provided, email not sent"
            })
        
    except Exception as e:
        return jsonify({
            "status": "error",
            "message": f"Error in password reset: {str(e)}"
        }), 500

if __name__ == '__main__':
    print("=" * 60)
    print("NSL PLATFORM EMAIL API")
    print("=" * 60)
    print("Available endpoints:")
    print("GET  /api/v2/email/health")
    print("POST /api/v2/email/validate")
    print("POST /api/v2/email/send")
    print("POST /api/v2/email/register")
    print("POST /api/v2/email/login")
    print("POST /api/v2/email/reset-password")
    print("=" * 60)
    print("Starting server on http://localhost:5000")
    print("=" * 60)
    
    app.run(debug=True, host='0.0.0.0', port=5000)
