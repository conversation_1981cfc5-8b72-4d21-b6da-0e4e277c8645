# NSL Platform Email System Setup Guide

## 🚀 Complete Email System with SMTP & Gmail Integration

This system provides:
- ✅ Email input validation
- ✅ SMTP email sending
- ✅ Flask API endpoints
- ✅ Postman collection for testing
- ✅ Gmail integration for direct login

## 📋 Prerequisites

1. **Python 3.7+** installed
2. **Gmail account** with App Password enabled
3. **Postman** for API testing

## 🔧 Installation Steps

### 1. Install Dependencies
```bash
pip install -r requirements.txt
```

### 2. Gmail Setup for SMTP

#### Enable 2-Factor Authentication:
1. Go to [Google Account Settings](https://myaccount.google.com/)
2. Click "Security" → "2-Step Verification"
3. Follow the setup process

#### Generate App Password:
1. Go to [Google Account Settings](https://myaccount.google.com/)
2. Click "Security" → "App passwords"
3. Select "Mail" and "Windows Computer" (or Other)
4. Copy the 16-character app password

#### Update Configuration:
Replace these values in the Postman collection variables:
- `sender_email`: Your Gmail address
- `sender_password`: Your 16-character app password

### 3. Start the API Server
```bash
python email_api.py
```

The server will start on `http://localhost:5000`

## 📡 API Endpoints

| Method | Endpoint | Description |
|--------|----------|-------------|
| GET | `/api/v2/email/health` | Health check |
| POST | `/api/v2/email/validate` | Validate email input |
| POST | `/api/v2/email/send` | Send custom email |
| POST | `/api/v2/email/register` | Register user + welcome email |
| POST | `/api/v2/email/login` | Login + notification email |
| POST | `/api/v2/email/reset-password` | Send password reset email |

## 🧪 Testing with Postman

### 1. Import Collection
1. Open Postman
2. Click "Import"
3. Select `NSL_Email_API.postman_collection.json`

### 2. Configure Variables
1. Click on the collection
2. Go to "Variables" tab
3. Update:
   - `sender_email`: Your Gmail address
   - `sender_password`: Your Gmail app password

### 3. Test Endpoints

#### Health Check:
```
GET http://localhost:5000/api/v2/email/health
```

#### Register User with Welcome Email:
```json
POST http://localhost:5000/api/v2/email/register
{
    "email": "<EMAIL>",
    "username": "testuser12",
    "first_name": "Durga",
    "last_name": "Prasad",
    "password": "test@123",
    "tenant_id": "t001",
    "sender_email": "<EMAIL>",
    "sender_password": "your-app-password"
}
```

#### Login with Email Notification:
```json
POST http://localhost:5000/api/v2/email/login
{
    "email": "<EMAIL>",
    "password": "test@123",
    "send_notification": true,
    "sender_email": "<EMAIL>",
    "sender_password": "your-app-password"
}
```

## 🔐 Gmail Integration Flow

### Direct Gmail Login Process:
1. **Hit the Login API** → Validates email and sends notification
2. **Check Gmail** → User receives login notification
3. **Auto-redirect** → Can be configured to redirect to Gmail

### Example Response:
```json
{
    "status": "success",
    "message": "Email validated for login",
    "data": {
        "username": "<EMAIL>",
        "password": "test@123",
        "email": "<EMAIL>"
    },
    "email": "<EMAIL>",
    "is_valid": true,
    "context": "login",
    "token": "uuid-token-here",
    "expires_in": 3600,
    "email_sent": {
        "status": "success",
        "message": "Email sent successfully",
        "sent": true
    }
}
```

## 🎯 Usage Examples

### 1. Email Validation Only:
```python
from email_input_handler import EmailInputHandler

handler = EmailInputHandler()
result = handler.validate_email("<EMAIL>")
print(result)  # (True, "Valid email")
```

### 2. Send Custom Email:
```python
result = handler.send_email(
    sender_email="<EMAIL>",
    sender_password="your-app-password",
    recipient_email="<EMAIL>",
    subject="Test Email",
    message="<h1>Hello World!</h1>",
    email_type="html"
)
```

### 3. Registration with Welcome Email:
```python
from email_input_handler import register_user_email_input

result = register_user_email_input(
    email="<EMAIL>",
    username="newuser",
    first_name="John",
    last_name="Doe"
)
```

## 🔧 Configuration Options

### SMTP Settings:
The system supports multiple email providers:
- **Gmail**: smtp.gmail.com:587
- **Outlook**: smtp-mail.outlook.com:587
- **Yahoo**: smtp.mail.yahoo.com:587

### Environment Variables:
```bash
export SMTP_EMAIL="<EMAIL>"
export SMTP_PASSWORD="your-app-password"
export DEFAULT_SENDER="<EMAIL>"
```

## 🚨 Troubleshooting

### Common Issues:

1. **"Authentication failed"**
   - Ensure 2FA is enabled on Gmail
   - Use App Password, not regular password
   - Check email/password spelling

2. **"Connection refused"**
   - Check internet connection
   - Verify SMTP server settings
   - Try different port (465 for SSL)

3. **"Invalid email format"**
   - Check email regex pattern
   - Ensure proper @ symbol usage
   - Verify domain format

### Debug Mode:
Run the API in debug mode:
```bash
python email_api.py
```

## 📞 Support

For issues or questions:
1. Check the console output for error messages
2. Verify Gmail app password setup
3. Test with simple email first
4. Check Postman collection variables

## 🎉 Success!

Once everything is set up:
1. **Start the API** → `python email_api.py`
2. **Open Postman** → Import collection
3. **Hit Login API** → User gets email notification
4. **Check Gmail** → Email received successfully!

The system is now ready for production use with your NSL Platform! 🚀
