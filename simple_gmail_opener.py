#!/usr/bin/env python3
"""
Simple Gmail Opener - Works Immediately
No Flask, no SMTP, just opens Gmail directly
Perfect for immediate testing
"""

import webbrowser
import time
from datetime import datetime

# Your Gmail Configuration
GMAIL_EMAIL = "<EMAIL>"

def open_gmail_directly():
    """Open Gmail directly in browser"""
    try:
        print("🚀 Opening Gmail for:", GMAIL_EMAIL)
        print("⏰ Time:", datetime.now().strftime('%Y-%m-%d %H:%M:%S'))
        
        # Gmail URL
        gmail_url = "https://mail.google.com/mail/u/0/#inbox"
        
        # Open Gmail
        webbrowser.open(gmail_url)
        
        print("✅ Gmail opened successfully!")
        print("🔗 URL:", gmail_url)
        print("📧 Account:", GMAIL_EMAIL)
        
        return True
        
    except Exception as e:
        print(f"❌ Error opening Gmail: {str(e)}")
        return False

def simulate_login_and_open_gmail(user_email=None):
    """Simulate login process and open Gmail"""
    if not user_email:
        user_email = GMAIL_EMAIL
    
    print("=" * 60)
    print("🔐 NSL PLATFORM LOGIN SIMULATION")
    print("=" * 60)
    print(f"📧 Email: {user_email}")
    print(f"👤 User: {user_email.split('@')[0].replace('.', ' ').title()}")
    print(f"⏰ Login Time: {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}")
    print(f"🏢 Platform: NSL Platform")
    print(f"✅ Status: Login Successful")
    print("=" * 60)
    
    # Simulate processing
    print("🔄 Processing login...")
    time.sleep(1)
    
    print("📧 Preparing email notification...")
    time.sleep(1)
    
    print("🌐 Opening Gmail...")
    success = open_gmail_directly()
    
    if success:
        print("=" * 60)
        print("🎉 SUCCESS!")
        print("✅ Login completed")
        print("✅ Gmail opened")
        print("✅ Check your inbox for notifications")
        print("=" * 60)
        return True
    else:
        print("❌ Failed to open Gmail")
        return False

def main():
    """Main function"""
    print("=" * 70)
    print("🚀 SIMPLE GMAIL OPENER FOR SIVANI")
    print("=" * 70)
    print(f"📧 Gmail Account: {GMAIL_EMAIL}")
    print("🎯 Purpose: Direct Gmail access after API simulation")
    print("=" * 70)
    
    while True:
        print("\n🔧 Choose an option:")
        print("1. Open Gmail directly")
        print("2. Simulate login and open Gmail")
        print("3. Exit")
        
        try:
            choice = input("\n👉 Enter your choice (1-3): ").strip()
            
            if choice == "1":
                print("\n🌐 Opening Gmail directly...")
                open_gmail_directly()
                
            elif choice == "2":
                print("\n🔐 Starting login simulation...")
                simulate_login_and_open_gmail()
                
            elif choice == "3":
                print("\n👋 Goodbye!")
                break
                
            else:
                print("\n❌ Invalid choice. Please enter 1, 2, or 3.")
                
        except KeyboardInterrupt:
            print("\n\n👋 Goodbye!")
            break
        except Exception as e:
            print(f"\n❌ Error: {str(e)}")

if __name__ == "__main__":
    # Quick test - automatically open Gmail
    print("🚀 QUICK START - Opening Gmail automatically...")
    simulate_login_and_open_gmail()
    
    # Ask if user wants interactive mode
    print("\n🔧 Would you like to continue with interactive mode? (y/n): ", end="")
    try:
        response = input().strip().lower()
        if response in ['y', 'yes']:
            main()
        else:
            print("👋 Done! Gmail should be open in your browser.")
    except:
        print("\n👋 Done! Gmail should be open in your browser.")
