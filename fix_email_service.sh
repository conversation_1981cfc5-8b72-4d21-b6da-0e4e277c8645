#!/bin/bash

# Email Service Setup and Fix Script
echo "🔧 Fixing Email Service Dependencies..."

# Check if we're in the right directory
if [ ! -d "adapters/email_service" ]; then
    echo "❌ Error: Not in workflow-system directory"
    echo "Please run this script from /home/<USER>/workflow-system/"
    exit 1
fi

echo "📦 Installing required Python packages..."

# Install core dependencies
pip3 install aiofiles
pip3 install python-multipart
pip3 install fastapi
pip3 install uvicorn[standard]
pip3 install jinja2
pip3 install python-dotenv
pip3 install email-validator
pip3 install structlog
pip3 install cryptography

echo "✅ Dependencies installed successfully!"

# Check if main.py exists and try to run it
if [ -f "adapters/email_service/app/main.py" ]; then
    echo "🚀 Testing email service..."
    cd adapters/email_service
    python3 -c "
import sys
sys.path.append('/home/<USER>/workflow-system')
try:
    from app.main import app
    print('✅ Email service imports successfully!')
except ImportError as e:
    print(f'❌ Import error: {e}')
    print('📝 Additional dependencies may be needed')
except Exception as e:
    print(f'⚠️  Other error: {e}')
"
    cd ../..
else
    echo "❌ main.py not found in expected location"
fi

echo "🎯 Next steps:"
echo "1. Navigate to your email service directory"
echo "2. Run: python3 -m app.main"
echo "3. Or run: uvicorn app.main:app --host 0.0.0.0 --port 8000"

echo "✅ Setup complete!"
