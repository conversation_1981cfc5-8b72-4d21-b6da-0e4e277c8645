#!/usr/bin/env python3
"""
Gmail Direct Access System
Complete email system with SMTP and direct Gmail opening
Email: <EMAIL>
"""

import smtplib
import ssl
import webbrowser
import time
from email.mime.text import MIMEText
from email.mime.multipart import MIME<PERSON>ultipart
from flask import Flask, request, jsonify
from flask_cors import CORS
import json
import threading
from datetime import datetime
import uuid

# Gmail Configuration
GMAIL_CONFIG = {
    "email": "<EMAIL>",
    "password": "YOUR_16_CHAR_APP_PASSWORD_HERE",  # Replace with Gmail App Password
    "smtp_server": "smtp.gmail.com",
    "smtp_port": 587
}

app = Flask(__name__)
CORS(app)

class GmailDirectSystem:
    def __init__(self):
        self.gmail_email = GMAIL_CONFIG["email"]
        self.gmail_password = GMAIL_CONFIG["password"]
        self.smtp_server = GMAIL_CONFIG["smtp_server"]
        self.smtp_port = GMAIL_CONFIG["smtp_port"]
    
    def send_email_and_open_gmail(self, recipient_email, subject, message, open_gmail=True):
        """
        Send email via SMTP and optionally open Gmail
        """
        try:
            # Create message
            msg = MIMEMultipart()
            msg['From'] = self.gmail_email
            msg['To'] = recipient_email
            msg['Subject'] = subject
            
            # Add body to email
            msg.attach(MIMEText(message, 'html'))
            
            # Create SMTP session
            server = smtplib.SMTP(self.smtp_server, self.smtp_port)
            server.starttls()  # Enable security
            server.login(self.gmail_email, self.gmail_password)
            
            # Send email
            text = msg.as_string()
            server.sendmail(self.gmail_email, recipient_email, text)
            server.quit()
            
            result = {
                "status": "success",
                "message": "Email sent successfully",
                "sender": self.gmail_email,
                "recipient": recipient_email,
                "subject": subject,
                "timestamp": datetime.now().isoformat(),
                "gmail_opened": False
            }
            
            # Open Gmail in browser
            if open_gmail:
                gmail_url = "https:://mail.google.com/mail/u/0/#inbox"
                webbrowser.open(gmail_url)
                result["gmail_opened"] = True
                result["gmail_url"] = gmail_url
            
            return result
            
        except Exception as e:
            return {
                "status": "error",
                "message": f"Failed to send email: {str(e)}",
                "error_details": str(e),
                "gmail_opened": False
            }
    
    def create_login_notification_email(self, user_email, user_name="User"):
        """Create login notification email content"""
        subject = "Login Notification - NSL Platform"
        
        message = f"""
        <html>
        <body style="font-family: Arial, sans-serif; max-width: 600px; margin: 0 auto;">
            <div style="background-color: #007bff; color: white; padding: 20px; text-align: center;">
                <h1>NSL Platform</h1>
                <h2>Login Notification</h2>
            </div>
            
            <div style="padding: 20px; background-color: #f8f9fa;">
                <h3>Hello {user_name},</h3>
                <p>We detected a successful login to your NSL Platform account.</p>
                
                <div style="background-color: white; padding: 15px; border-radius: 5px; margin: 20px 0;">
                    <h4>Login Details:</h4>
                    <ul>
                        <li><strong>Email:</strong> {user_email}</li>
                        <li><strong>Time:</strong> {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}</li>
                        <li><strong>Platform:</strong> NSL Platform</li>
                        <li><strong>Status:</strong> ✅ Successful</li>
                    </ul>
                </div>
                
                <div style="background-color: #d4edda; padding: 15px; border-radius: 5px; border-left: 4px solid #28a745;">
                    <p><strong>✅ Login Successful!</strong></p>
                    <p>You can now access your NSL Platform dashboard.</p>
                </div>
                
                <p>If this wasn't you, please contact our support team immediately.</p>
                
                <div style="text-align: center; margin-top: 30px;">
                    <a href="https://mail.google.com" style="background-color: #007bff; color: white; padding: 10px 20px; text-decoration: none; border-radius: 5px;">
                        Open Gmail
                    </a>
                </div>
                
                <p style="margin-top: 30px;">Best regards,<br>
                <strong>NSL Platform Team</strong><br>
                Email: {self.gmail_email}</p>
            </div>
            
            <div style="background-color: #6c757d; color: white; padding: 10px; text-align: center; font-size: 12px;">
                <p>This is an automated message from NSL Platform</p>
            </div>
        </body>
        </html>
        """
        
        return subject, message

# Initialize Gmail system
gmail_system = GmailDirectSystem()

# API Endpoints
@app.route('/api/v2/email/health', methods=['GET'])
def health_check():
    """Health check endpoint"""
    return jsonify({
        "status": "success",
        "message": "Gmail Direct System is running",
        "gmail_email": GMAIL_CONFIG["email"],
        "timestamp": datetime.now().isoformat(),
        "version": "1.0"
    })

@app.route('/api/v2/email/login', methods=['POST'])
def direct_gmail_login():
    """
    Login API that sends email and opens Gmail directly
    POST /api/v2/email/login
    Body: {
        "email": "<EMAIL>",
        "password": "userpassword",
        "open_gmail": true
    }
    """
    try:
        data = request.get_json()
        
        if not data:
            return jsonify({
                "status": "error",
                "message": "No JSON data provided"
            }), 400
        
        user_email = data.get('email', '').strip()
        user_password = data.get('password', '')
        open_gmail = data.get('open_gmail', True)
        
        if not user_email:
            return jsonify({
                "status": "error",
                "message": "Email is required"
            }), 400
        
        # Simulate login validation (you can add real validation here)
        if not user_password:
            return jsonify({
                "status": "error",
                "message": "Password is required"
            }), 400
        
        # Extract user name from email
        user_name = user_email.split('@')[0].replace('.', ' ').title()
        
        # Create login notification email
        subject, message = gmail_system.create_login_notification_email(user_email, user_name)
        
        # Send email and open Gmail
        email_result = gmail_system.send_email_and_open_gmail(
            recipient_email=user_email,
            subject=subject,
            message=message,
            open_gmail=open_gmail
        )
        
        # Create response
        response = {
            "status": "success",
            "message": "Login successful - Email sent and Gmail opened",
            "user_email": user_email,
            "user_name": user_name,
            "login_token": str(uuid.uuid4()),
            "expires_in": 3600,
            "email_result": email_result,
            "gmail_opened": email_result.get("gmail_opened", False)
        }
        
        if email_result["status"] == "error":
            response["status"] = "partial_success"
            response["message"] = "Login successful but email sending failed"
        
        return jsonify(response)
        
    except Exception as e:
        return jsonify({
            "status": "error",
            "message": f"Login failed: {str(e)}",
            "error_details": str(e)
        }), 500

@app.route('/api/v2/email/send-and-open', methods=['POST'])
def send_email_and_open():
    """
    Send custom email and open Gmail
    POST /api/v2/email/send-and-open
    Body: {
        "recipient_email": "<EMAIL>",
        "subject": "Test Email",
        "message": "Hello World",
        "open_gmail": true
    }
    """
    try:
        data = request.get_json()
        
        if not data:
            return jsonify({
                "status": "error",
                "message": "No JSON data provided"
            }), 400
        
        recipient_email = data.get('recipient_email', '').strip()
        subject = data.get('subject', 'NSL Platform Notification')
        message = data.get('message', 'Hello from NSL Platform!')
        open_gmail = data.get('open_gmail', True)
        
        if not recipient_email:
            return jsonify({
                "status": "error",
                "message": "Recipient email is required"
            }), 400
        
        # Send email and open Gmail
        result = gmail_system.send_email_and_open_gmail(
            recipient_email=recipient_email,
            subject=subject,
            message=message,
            open_gmail=open_gmail
        )
        
        return jsonify(result)
        
    except Exception as e:
        return jsonify({
            "status": "error",
            "message": f"Failed to send email: {str(e)}",
            "error_details": str(e)
        }), 500

@app.route('/api/v2/email/open-gmail', methods=['POST'])
def open_gmail_directly():
    """
    Open Gmail directly in browser
    POST /api/v2/email/open-gmail
    """
    try:
        gmail_url = "https://mail.google.com/mail/u/0/#inbox"
        webbrowser.open(gmail_url)
        
        return jsonify({
            "status": "success",
            "message": "Gmail opened successfully",
            "gmail_url": gmail_url,
            "gmail_email": GMAIL_CONFIG["email"],
            "timestamp": datetime.now().isoformat()
        })
        
    except Exception as e:
        return jsonify({
            "status": "error",
            "message": f"Failed to open Gmail: {str(e)}",
            "error_details": str(e)
        }), 500

if __name__ == '__main__':
    print("=" * 70)
    print("🚀 GMAIL DIRECT ACCESS SYSTEM")
    print("=" * 70)
    print(f"📧 Gmail Account: {GMAIL_CONFIG['email']}")
    print("🌐 Available endpoints:")
    print("   GET  /api/v2/email/health")
    print("   POST /api/v2/email/login")
    print("   POST /api/v2/email/send-and-open")
    print("   POST /api/v2/email/open-gmail")
    print("=" * 70)
    print("🎯 USAGE:")
    print("   1. Hit /api/v2/email/login endpoint")
    print("   2. Email will be sent automatically")
    print("   3. Gmail will open in your browser")
    print("=" * 70)
    print("⚠️  IMPORTANT:")
    print("   For Gmail SMTP, you need to:")
    print("   1. Enable 2-Factor Authentication")
    print("   2. Generate App Password")
    print("   3. Replace 'Sivani@123' with App Password")
    print("=" * 70)
    print("🚀 Starting server on http://localhost:5000")
    print("=" * 70)
    
    app.run(debug=True, host='0.0.0.0', port=5000)
